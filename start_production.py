#!/usr/bin/env python3
"""
生产环境启动脚本
使用Gunicorn提供更好的性能和稳定性
"""

import os
import sys
import subprocess
import signal
import time
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProductionServer:
    """生产环境服务器管理"""
    
    def __init__(self):
        self.pid_file = 'kkuguan.pid'
        self.log_file = 'logs/gunicorn.log'
        self.access_log = 'logs/gunicorn_access.log'
        self.error_log = 'logs/gunicorn_error.log'
        
        # 确保日志目录存在
        Path('logs').mkdir(exist_ok=True)
    
    def get_gunicorn_config(self):
        """获取Gunicorn配置"""
        return {
            'bind': '0.0.0.0:5003',
            'workers': 4,  # CPU核心数 * 2
            'worker_class': 'gevent',  # 异步工作模式
            'worker_connections': 1000,
            'max_requests': 1000,  # 防止内存泄漏
            'max_requests_jitter': 100,
            'timeout': 120,  # 请求超时
            'keepalive': 5,  # 保持连接
            'preload_app': True,  # 预加载应用
            'daemon': False,  # 不使用守护进程模式（便于管理）
            'pidfile': self.pid_file,
            'accesslog': self.access_log,
            'errorlog': self.error_log,
            'loglevel': 'info',
            'access_log_format': '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s',
        }
    
    def build_gunicorn_command(self):
        """构建Gunicorn启动命令"""
        config = self.get_gunicorn_config()
        
        cmd = ['gunicorn']
        
        # 添加配置参数
        for key, value in config.items():
            if isinstance(value, bool):
                if value:
                    cmd.append(f'--{key.replace("_", "-")}')
            else:
                cmd.extend([f'--{key.replace("_", "-")}', str(value)])
        
        # 添加应用模块
        cmd.append('wsgi:app')
        
        return cmd
    
    def is_running(self):
        """检查服务是否正在运行"""
        if not os.path.exists(self.pid_file):
            return False
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            # 检查进程是否存在
            os.kill(pid, 0)
            return True
        except (OSError, ValueError):
            # 进程不存在或PID文件损坏
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
            return False
    
    def start(self):
        """启动服务"""
        if self.is_running():
            logger.info("🟡 服务已在运行中")
            return True
        
        logger.info("🚀 启动生产环境服务器...")
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 构建启动命令
        cmd = self.build_gunicorn_command()
        
        try:
            logger.info(f"📝 启动命令: {' '.join(cmd)}")
            
            # 启动服务
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # 等待一下确保启动成功
            time.sleep(3)
            
            if process.poll() is None:
                logger.info("✅ 生产环境服务器启动成功")
                logger.info(f"🌐 服务地址: http://localhost:5003")
                logger.info(f"📊 工作进程: 4个")
                logger.info(f"📝 访问日志: {self.access_log}")
                logger.info(f"❌ 错误日志: {self.error_log}")
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ 启动失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 启动异常: {e}")
            return False
    
    def stop(self):
        """停止服务"""
        if not self.is_running():
            logger.info("🟡 服务未运行")
            return True
        
        try:
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            
            logger.info(f"🛑 停止服务 (PID: {pid})...")
            
            # 发送TERM信号
            os.kill(pid, signal.SIGTERM)
            
            # 等待进程结束
            for i in range(10):
                try:
                    os.kill(pid, 0)
                    time.sleep(1)
                except OSError:
                    break
            else:
                # 强制杀死
                logger.warning("⚠️ 强制停止服务...")
                os.kill(pid, signal.SIGKILL)
            
            # 清理PID文件
            if os.path.exists(self.pid_file):
                os.remove(self.pid_file)
            
            logger.info("✅ 服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"❌ 停止服务失败: {e}")
            return False
    
    def restart(self):
        """重启服务"""
        logger.info("🔄 重启服务...")
        self.stop()
        time.sleep(2)
        return self.start()
    
    def status(self):
        """查看服务状态"""
        if self.is_running():
            with open(self.pid_file, 'r') as f:
                pid = int(f.read().strip())
            logger.info(f"✅ 服务正在运行 (PID: {pid})")
            logger.info(f"🌐 服务地址: http://localhost:5003")
        else:
            logger.info("❌ 服务未运行")
    
    def check_dependencies(self):
        """检查依赖"""
        try:
            import gunicorn
            logger.info("✅ Gunicorn已安装")
            return True
        except ImportError:
            logger.error("❌ Gunicorn未安装，请运行: pip install gunicorn")
            return False
    
    def logs(self, log_type='access', lines=50):
        """查看日志"""
        log_file = self.access_log if log_type == 'access' else self.error_log
        
        if not os.path.exists(log_file):
            logger.info(f"📝 日志文件不存在: {log_file}")
            return
        
        try:
            cmd = ['tail', '-n', str(lines), log_file]
            result = subprocess.run(cmd, capture_output=True, text=True)
            print(result.stdout)
        except Exception as e:
            logger.error(f"❌ 读取日志失败: {e}")

def main():
    """主函数"""
    server = ProductionServer()
    
    if len(sys.argv) < 2:
        print("🚀 KKUGUAN 生产环境服务器管理")
        print("用法:")
        print("  python3 start_production.py start    # 启动服务")
        print("  python3 start_production.py stop     # 停止服务")
        print("  python3 start_production.py restart  # 重启服务")
        print("  python3 start_production.py status   # 查看状态")
        print("  python3 start_production.py logs     # 查看访问日志")
        print("  python3 start_production.py errors   # 查看错误日志")
        return
    
    command = sys.argv[1].lower()
    
    if command == 'start':
        server.start()
    elif command == 'stop':
        server.stop()
    elif command == 'restart':
        server.restart()
    elif command == 'status':
        server.status()
    elif command == 'logs':
        server.logs('access')
    elif command == 'errors':
        server.logs('error')
    else:
        logger.error(f"❌ 未知命令: {command}")

if __name__ == "__main__":
    main()
