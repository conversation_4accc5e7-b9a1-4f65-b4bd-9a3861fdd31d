"""
库存API模块
处理库存相关的API请求
"""

import logging
from flask import Blueprint, jsonify, request, current_app
from app.web.auth import login_required
from app.utils.decorators import handle_errors

logger = logging.getLogger(__name__)

# 创建库存API蓝图
inventory_api = Blueprint('inventory_api', __name__)

@inventory_api.route('/sku_list')
@login_required
@handle_errors
def api_sku_list():
    """SKU列表API"""
    try:
        cache_service = current_app.cache_service

        def get_inventory_data():
            from database_config import get_sku_list
            return get_sku_list()

        # 使用缓存
        data = cache_service.get_or_set(
            'sku_list_all',
            get_inventory_data,
            ttl=1800  # 30分钟缓存
        )

        # 返回前端期望的格式
        return jsonify({
            'sku_list': data,
            'ordered_suppliers': []  # 可以后续添加已下单供应商逻辑
        })

    except Exception as e:
        logger.error(f'库存列表API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@inventory_api.route('/sku_detail')
@login_required
@handle_errors
def api_sku_detail():
    """SKU详情API"""
    try:
        sku = request.args.get('sku')
        if not sku:
            return jsonify({'error': '未提供SKU'}), 400
        
        cache_service = current_app.cache_service
        cache_key = f"sku_detail_{sku}"
        
        def get_sku_detail_data():
            from database_config import get_sku_detail
            return get_sku_detail(sku)
        
        # 使用缓存
        data = cache_service.get_or_set(
            cache_key,
            get_sku_detail_data,
            ttl=600  # 10分钟缓存
        )
        
        if not data:
            return jsonify({'error': 'SKU不存在'}), 404
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'SKU详情API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@inventory_api.route('/inventory_alerts')
@login_required
@handle_errors
def api_inventory_alerts():
    """库存预警API"""
    try:
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        cache_service = current_app.cache_service
        cache_key = f"inventory_alerts_{start_date}_{end_date}"
        
        def get_alerts_data():
            from database_config import get_inventory_alerts
            return get_inventory_alerts(start_date, end_date)
        
        # 使用缓存
        data = cache_service.get_or_set(
            cache_key,
            get_alerts_data,
            ttl=600  # 10分钟缓存
        )
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'库存预警API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@inventory_api.route('/restock_calendar')
@login_required
@handle_errors
def api_restock_calendar():
    """补货日历API"""
    try:
        cache_service = current_app.cache_service
        
        def get_restock_data():
            from database_config import get_restock_calendar
            return get_restock_calendar()
        
        # 使用缓存
        data = cache_service.get_or_set(
            'restock_calendar',
            get_restock_data,
            ttl=3600  # 1小时缓存
        )
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'补货计划API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500
