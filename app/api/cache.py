"""
缓存管理API模块
处理缓存相关的API请求
"""

import logging
from flask import Blueprint, jsonify, current_app
from app.web.auth import login_required
from app.utils.decorators import handle_errors

logger = logging.getLogger(__name__)

# 创建缓存API蓝图
cache_api = Blueprint('cache_api', __name__)

@cache_api.route('/cache_info')
@login_required
@handle_errors
def api_cache_info():
    """缓存信息API"""
    try:
        cache_service = current_app.cache_service
        stats = cache_service.get_stats()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f'获取缓存信息错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@cache_api.route('/clear_cache', methods=['POST'])
@login_required
@handle_errors
def api_clear_cache():
    """清除缓存API"""
    try:
        cache_service = current_app.cache_service
        
        # 清除所有缓存
        cache_service.clear_pattern('')
        
        logger.info("手动清除所有缓存")
        
        return jsonify({
            'success': True,
            'message': '缓存已清除'
        })
        
    except Exception as e:
        logger.error(f'清除缓存错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@cache_api.route('/clear_sku_cache', methods=['POST'])
@login_required
@handle_errors
def api_clear_sku_cache():
    """清除SKU相关缓存API"""
    try:
        cache_service = current_app.cache_service
        
        # 清除SKU相关缓存
        cleared_count = cache_service.clear_pattern('sku_')
        
        logger.info(f"手动清除SKU缓存: {cleared_count} 个")
        
        return jsonify({
            'success': True,
            'message': f'已清除 {cleared_count} 个SKU缓存'
        })
        
    except Exception as e:
        logger.error(f'清除SKU缓存错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@cache_api.route('/warmup_status')
@login_required
@handle_errors
def api_warmup_status():
    """预热状态API"""
    try:
        from app.services.warmup_service import get_warmup_status
        status = get_warmup_status()
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f'获取预热状态错误: {e}')
        return jsonify({'error': '服务器错误'}), 500
