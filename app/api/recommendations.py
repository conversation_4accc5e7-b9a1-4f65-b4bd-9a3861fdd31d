"""
推荐API模块
处理选品推荐相关的API请求
"""

import logging
from flask import Blueprint, jsonify, request, current_app
from app.web.auth import login_required
from app.utils.decorators import handle_errors

logger = logging.getLogger(__name__)

# 创建推荐API蓝图
recommendations_api = Blueprint('recommendations_api', __name__)

@recommendations_api.route('/recommendations')
@login_required
@handle_errors
def api_recommendations():
    """选品推荐API"""
    try:
        # 获取查询参数
        rec_type = request.args.get('type', 'all')
        supplier = request.args.get('supplier', 'W3121')
        sort_by = request.args.get('sort_by', '30d')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 24))
        
        cache_service = current_app.cache_service
        cache_key = f"recommendations_{rec_type}_{supplier}_{sort_by}_{page}_{per_page}"
        
        def get_recommendations_data():
            from database_config import get_recommendations
            return get_recommendations(
                rec_type=rec_type,
                supplier=supplier,
                sort_by=sort_by,
                page=page,
                per_page=per_page
            )
        
        # 使用缓存
        data = cache_service.get_or_set(
            cache_key,
            get_recommendations_data,
            ttl=1800  # 30分钟缓存
        )
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'推荐API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500
