"""
物流追踪API模块
处理物流追踪相关的API请求
"""

import logging
from flask import Blueprint, jsonify, request, current_app
from app.web.auth import login_required
from app.utils.decorators import handle_errors

logger = logging.getLogger(__name__)

# 创建物流追踪API蓝图
tracking_api = Blueprint('tracking_api', __name__)

@tracking_api.route('/tracking')
@login_required
@handle_errors
def api_tracking():
    """物流追踪数据API"""
    try:
        cache_service = current_app.cache_service
        
        def get_tracking_data():
            from database_config import get_tracking_data
            return get_tracking_data()
        
        # 使用缓存
        data = cache_service.get_or_set(
            'tracking_data_all',
            get_tracking_data,
            ttl=180  # 3分钟缓存
        )
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'物流追踪API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@tracking_api.route('/tracking/<tracking_number>')
@login_required
@handle_errors
def api_tracking_detail(tracking_number):
    """单个物流追踪详情API"""
    try:
        cache_service = current_app.cache_service
        cache_key = f"tracking_detail_{tracking_number}"
        
        def get_tracking_detail():
            from database_config import get_tracking_by_number
            return get_tracking_by_number(tracking_number)
        
        # 使用缓存
        data = cache_service.get_or_set(
            cache_key,
            get_tracking_detail,
            ttl=300  # 5分钟缓存
        )
        
        if not data:
            return jsonify({'error': '跟踪号不存在'}), 404
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'物流详情API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@tracking_api.route('/tracking/update', methods=['POST'])
@login_required
@handle_errors
def api_tracking_update():
    """更新物流追踪信息API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400
        
        order_number = data.get('order_number')
        tracking_number = data.get('tracking_number')
        remark = data.get('remark', '')
        is_uninvited_review = data.get('is_uninvited_review', False)
        
        if not order_number:
            return jsonify({'error': '缺少订单号'}), 400
        
        # 更新数据库
        from database_config import update_tracking_remark
        success = update_tracking_remark(
            order_number, 
            tracking_number, 
            remark, 
            is_uninvited_review
        )
        
        if success:
            # 清除相关缓存
            cache_service = current_app.cache_service
            cache_service.clear_pattern('tracking_')
            
            return jsonify({'success': True, 'message': '更新成功'})
        else:
            return jsonify({'error': '更新失败'}), 500
        
    except Exception as e:
        logger.error(f'更新物流信息错误: {e}')
        return jsonify({'error': '服务器错误'}), 500

@tracking_api.route('/tracking/events')
@login_required
@handle_errors
def api_tracking_events():
    """物流事件API"""
    try:
        cache_service = current_app.cache_service

        def get_tracking_events():
            from database_config import get_tracking_events
            return get_tracking_events()

        # 使用缓存
        data = cache_service.get_or_set(
            'tracking_events',
            get_tracking_events,
            ttl=300  # 5分钟缓存
        )

        return jsonify(data)

    except Exception as e:
        logger.error(f'物流事件API错误: {e}')
        return jsonify({'error': '服务器错误'}), 500
