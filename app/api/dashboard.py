"""
仪表板API模块
处理仪表板相关的API请求
"""

import time
import logging
from flask import Blueprint, jsonify, current_app
from app.web.auth import login_required
from app.utils.decorators import handle_errors
from database_config import get_dashboard_stats, get_inventory_alerts

logger = logging.getLogger(__name__)

# 创建仪表板API蓝图
dashboard_api = Blueprint('dashboard_api', __name__)

@dashboard_api.route('/stats')
@login_required
@handle_errors
def api_stats():
    """仪表板统计API"""
    try:
        cache_service = current_app.cache_service
        
        # 使用缓存获取统计数据
        stats = cache_service.get_or_set(
            'dashboard_stats',
            get_dashboard_stats,
            ttl=300  # 5分钟缓存
        )
        
        logger.info("仪表板统计数据获取成功")
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f'获取统计数据时出错: {str(e)}')
        return jsonify({
            'total_skus': 0,
            'pending_restock': 0,
            'alerts_count': 0
        })

@dashboard_api.route('/dashboard_alerts')
@login_required
@handle_errors
def api_dashboard_alerts():
    """仪表板库存提醒API"""
    try:
        start_time = time.time()
        cache_service = current_app.cache_service
        cache_key = "dashboard_alerts"
        
        logger.info(f"🔍 仪表盘库存提醒API请求开始 - 缓存键: {cache_key}")
        
        def get_dashboard_alerts_data():
            """获取仪表板库存提醒数据，按预计可售天数排序"""
            query_start_time = time.time()
            logger.info("📊 开始执行仪表盘库存提醒数据库查询...")
            
            # 查询有库存的SKU，计算预计可售天数并排序
            # 注意：不在SQL中过滤天数，让代码逻辑来处理
            query = """
            SELECT
                i.sku,
                i.quantity as current_stock,
                p.product_name,
                p.supplier_code,
                i.updated_at,
                COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as total_available_stock,
                COALESCE(s.total_sales_30d, 0) as total_sales_30d,
                CASE
                    WHEN COALESCE(s.total_sales_30d, 0) > 0 THEN
                        ROUND((COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) * 30 / s.total_sales_30d, 1)
                    ELSE 999999
                END as estimated_days
            FROM inventory i
            LEFT JOIN products p ON i.sku = p.sku
            LEFT JOIN (
                SELECT sku, SUM(product_quantity) as total_sales_30d
                FROM orders
                WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY sku
            ) s ON i.sku = s.sku
            WHERE i.quantity > 0
            AND i.date = (SELECT MAX(i2.date) FROM inventory i2 WHERE i2.sku = i.sku)
            ORDER BY estimated_days ASC
            """
            
            from database_config import execute_query
            alerts = execute_query(query)
            
            processing_start_time = time.time()
            logger.info(f"🔥 预热: 查询到 {len(alerts)} 条有库存的SKU")
            
            # 处理数据
            alerts_with_days = []
            for alert in alerts:
                try:
                    estimated_days = float(alert.get('estimated_days', 0))
                    if estimated_days > 0 and estimated_days < 999999:  # 排除无销量的SKU
                        # 设置警告级别
                        if estimated_days <= 30:
                            alert_level = 'critical'
                        elif estimated_days <= 60:
                            alert_level = 'warning'
                        elif estimated_days <= 90:
                            alert_level = 'info'
                        else:
                            alert_level = 'normal'

                        alert_data = {
                            'sku': alert.get('sku', ''),
                            'product_name': alert.get('product_name', ''),
                            'current_stock': int(alert.get('current_stock', 0)),
                            'estimated_days': estimated_days,
                            'supplier_code': alert.get('supplier_code', ''),
                            'alert_level': alert_level,
                            'updated_at': alert.get('updated_at'),
                            'total_sales_30d': int(alert.get('total_sales_30d', 0))
                        }
                        alerts_with_days.append(alert_data)
                except (ValueError, TypeError) as e:
                    logger.warning(f"处理SKU数据时出错: {alert.get('sku', 'unknown')} - {e}")
                    continue

            # 按预计可售天数排序
            alerts_with_days.sort(key=lambda x: x['estimated_days'])

            # 优先返回≤60天的数据
            critical_alerts = [a for a in alerts_with_days if a['estimated_days'] <= 60]
            logger.info(f"🔥 找到 {len(critical_alerts)} 个≤60天的SKU")

            if len(critical_alerts) >= 10:
                result = critical_alerts[:30]
                logger.info(f"使用≤60天数据，返回{len(result)}个SKU")
            else:
                # 如果≤60天的数据不足，扩展到≤120天
                extended_alerts = [a for a in alerts_with_days if a['estimated_days'] <= 120]
                logger.info(f"🔥 找到 {len(extended_alerts)} 个≤120天的SKU")

                if len(extended_alerts) >= 10:
                    result = extended_alerts[:30]
                    logger.info(f"扩展到≤120天，返回{len(result)}个SKU")
                else:
                    # 如果还是不足，返回所有有销量的SKU（前30个）
                    result = alerts_with_days[:30]
                    logger.info(f"返回所有有销量的SKU，共{len(result)}个")
            
            processing_time = time.time() - processing_start_time
            total_time = time.time() - query_start_time
            logger.info(f"📊 数据处理完成，耗时: {processing_time:.2f}秒，总耗时: {total_time:.2f}秒")
            logger.info(f"📊 返回 {len(result)} 个库存提醒，最紧急的预计可售天数: {result[0]['estimated_days'] if result else 'N/A'}")
            
            return result
        
        # 使用缓存管理器 - 1小时缓存
        data = cache_service.get_or_set(cache_key, get_dashboard_alerts_data, ttl=3600)
        
        # 记录API响应时间
        total_time = time.time() - start_time
        logger.info(f"✅ 仪表盘库存提醒API完成 - 总耗时: {total_time:.2f}秒，返回 {len(data)} 个提醒")
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f'获取仪表板提醒失败: {str(e)}')
        return jsonify([])

@dashboard_api.route('/dashboard_summary')
@login_required
@handle_errors
def api_dashboard_summary():
    """仪表板摘要信息API"""
    try:
        cache_service = current_app.cache_service
        
        def get_summary_data():
            from database_config import execute_query
            
            # 获取基础统计 - 使用计算的预计可售天数
            stats_query = """
            WITH sku_stats AS (
                SELECT
                    i.sku,
                    i.quantity,
                    CASE
                        WHEN COALESCE(s.total_sales_30d, 0) > 0 THEN
                            ROUND((COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) * 30 / s.total_sales_30d, 1)
                        ELSE 999999
                    END as estimated_days
                FROM inventory i
                LEFT JOIN (
                    SELECT sku, SUM(product_quantity) as total_sales_30d
                    FROM orders
                    WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY sku
                ) s ON i.sku = s.sku
                WHERE i.quantity > 0
                AND i.date = (SELECT MAX(i2.date) FROM inventory i2 WHERE i2.sku = i.sku)
            )
            SELECT
                COUNT(DISTINCT sku) as total_skus,
                COUNT(DISTINCT CASE WHEN estimated_days <= 30 THEN sku END) as critical_alerts,
                COUNT(DISTINCT CASE WHEN estimated_days <= 60 THEN sku END) as warning_alerts,
                SUM(quantity) as total_inventory
            FROM sku_stats
            """
            
            stats = execute_query(stats_query)
            
            if stats:
                return {
                    'total_skus': stats[0].get('total_skus', 0),
                    'critical_alerts': stats[0].get('critical_alerts', 0),
                    'warning_alerts': stats[0].get('warning_alerts', 0),
                    'total_inventory': stats[0].get('total_inventory', 0),
                    'last_updated': time.time()
                }
            
            return {
                'total_skus': 0,
                'critical_alerts': 0,
                'warning_alerts': 0,
                'total_inventory': 0,
                'last_updated': time.time()
            }
        
        # 使用缓存，5分钟TTL
        summary = cache_service.get_or_set(
            'dashboard_summary',
            get_summary_data,
            ttl=300
        )
        
        return jsonify(summary)
        
    except Exception as e:
        logger.error(f'获取仪表板摘要失败: {str(e)}')
        return jsonify({
            'total_skus': 0,
            'critical_alerts': 0,
            'warning_alerts': 0,
            'total_inventory': 0,
            'error': str(e)
        })
