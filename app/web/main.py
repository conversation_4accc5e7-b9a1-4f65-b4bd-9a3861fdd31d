"""
主页面模块
处理所有的页面路由
"""

import logging
from flask import Blueprint, render_template, current_app
from app.web.auth import login_required

logger = logging.getLogger(__name__)

# 创建主页面蓝图
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
@login_required
def index():
    """首页 - 重定向到仪表板"""
    return render_template('dashboard.html')

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """仪表板页面"""
    logger.info("访问仪表板页面")
    return render_template('dashboard.html')

@main_bp.route('/sku')
@main_bp.route('/sku-detail')
@main_bp.route('/sku_detail')
@login_required
def sku_detail():
    """SKU详情页面"""
    logger.info("访问SKU详情页面")
    return render_template('sku_detail.html')

@main_bp.route('/recommend')
@login_required
def recommend():
    """推荐页面"""
    logger.info("访问推荐页面")
    return render_template('recommend.html')

@main_bp.route('/restock')
@login_required
def restock():
    """补货计划页面"""
    logger.info("访问补货计划页面")
    return render_template('restock.html')

@main_bp.route('/alert')
@login_required
def alert():
    """库存预警页面"""
    logger.info("访问库存预警页面")
    return render_template('alert.html')

@main_bp.route('/tracking')
@login_required
def tracking():
    """物流追踪页面"""
    logger.info("访问物流追踪页面")
    return render_template('tracking.html')

@main_bp.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 简单的健康检查
        from database_config import execute_query
        
        # 使用缓存检查
        cache_service = current_app.cache_service
        cache_key = "health_check"
        
        def get_health_status():
            # 简单的数据库连接测试
            test_query = "SELECT 1 as test"
            result = execute_query(test_query)
            healthy = len(result) > 0
            
            return {
                'status': 'healthy' if healthy else 'unhealthy',
                'timestamp': time.time(),
                'database_connection': healthy,
                'cache_available': cache_service is not None
            }
        
        # 使用缓存管理器，短缓存时间（30秒）
        data = cache_service.get_or_set(cache_key, get_health_status, ttl=30)
        
        from flask import jsonify
        return jsonify(data)
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        from flask import jsonify
        return jsonify({
            'status': 'unhealthy',
            'error': str(e)
        }), 500

@main_bp.route('/favicon.ico')
@main_bp.route('/static/favicon.ico')
def favicon():
    """网站图标"""
    return '', 204

# 上下文处理器 - 为模板提供全局变量
@main_bp.app_context_processor
def inject_global_vars():
    """注入全局模板变量"""
    return {
        'app_name': 'KKUGUAN库存管理系统',
        'version': '2.0.0'
    }

# 模板过滤器
@main_bp.app_template_filter('datetime_format')
def datetime_format(value, format='%Y-%m-%d %H:%M:%S'):
    """日期时间格式化过滤器"""
    if value is None:
        return ""
    return value.strftime(format)

@main_bp.app_template_filter('number_format')
def number_format(value):
    """数字格式化过滤器"""
    if value is None:
        return "0"
    try:
        return f"{float(value):,.2f}"
    except (ValueError, TypeError):
        return str(value)
