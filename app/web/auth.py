"""
认证模块
处理用户登录、登出和权限验证
"""

import time
import logging
from functools import wraps
from flask import Blueprint, render_template, request, session, redirect, url_for, flash, current_app

logger = logging.getLogger(__name__)

# 创建认证蓝图
auth_bp = Blueprint('auth', __name__)

# 登录尝试记录
_login_attempts = {}

def is_ip_locked(ip):
    """检查IP是否被锁定"""
    max_attempts = current_app.config.get('MAX_LOGIN_ATTEMPTS', 5)
    lockout_duration = current_app.config.get('LOCKOUT_DURATION', 300)
    
    if ip in _login_attempts:
        attempts, last_attempt = _login_attempts[ip]
        if attempts >= max_attempts:
            if time.time() - last_attempt < lockout_duration:
                return True
            else:
                del _login_attempts[ip]
    return False

def record_login_attempt(ip, success=False):
    """记录登录尝试"""
    if success:
        _login_attempts.pop(ip, None)
    else:
        attempts, _ = _login_attempts.get(ip, (0, 0))
        _login_attempts[ip] = (attempts + 1, time.time())

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'logged_in' not in session:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        user_ip = request.remote_addr
        
        # 检查IP是否被锁定
        if is_ip_locked(user_ip):
            flash('登录尝试过多，请5分钟后再试！', 'error')
            return render_template('login.html')
        
        # 获取表单数据
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        # 验证输入
        if not username or not password:
            flash('请输入用户名和密码！', 'error')
            record_login_attempt(user_ip, success=False)
            return render_template('login.html')
        
        # 验证用户凭据
        login_users = current_app.config.get('LOGIN_USERS', {})
        if username in login_users and login_users[username] == password:
            # 登录成功
            session['logged_in'] = True
            session['username'] = username
            record_login_attempt(user_ip, success=True)
            flash('登录成功！', 'success')
            
            logger.info(f"用户登录成功: {username} from {user_ip}")
            
            # 重定向到目标页面
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('main.index'))
        else:
            # 登录失败
            record_login_attempt(user_ip, success=False)
            flash('用户名或密码错误！', 'error')
            logger.warning(f"登录失败: {username} from {user_ip}")
    
    return render_template('login.html')

@auth_bp.route('/logout')
def logout():
    """用户登出"""
    username = session.get('username', 'unknown')
    session.clear()
    flash('已成功退出登录！', 'success')
    logger.info(f"用户登出: {username}")
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile')
@login_required
def profile():
    """用户资料页面"""
    return render_template('profile.html', username=session.get('username'))

def get_current_user():
    """获取当前登录用户"""
    if 'logged_in' in session:
        return {
            'username': session.get('username'),
            'logged_in': True
        }
    return None

def check_permission(permission):
    """检查用户权限装饰器"""
    def decorator(f):
        @wraps(f)
        @login_required
        def decorated_function(*args, **kwargs):
            user = get_current_user()
            if not user:
                flash('需要登录才能访问此页面', 'error')
                return redirect(url_for('auth.login'))
            
            # 这里可以添加更复杂的权限检查逻辑
            # 目前所有登录用户都有相同权限
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
