"""
KKUGUAN 库存管理系统
应用工厂模式 - 主应用入口
"""

import os
import sys
import logging
from flask import Flask
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_app(config_name='default'):
    """应用工厂函数"""

    # 获取项目根目录 - 使用绝对路径确保正确
    current_dir = os.path.dirname(os.path.abspath(__file__))  # app目录
    basedir = os.path.dirname(current_dir)  # 项目根目录

    template_path = os.path.join(basedir, 'templates')
    static_path = os.path.join(basedir, 'static')

    # 创建Flask应用实例，指定模板和静态文件路径
    app = Flask(__name__,
                template_folder=template_path,
                static_folder=static_path)
    
    # 加载配置
    from config import config
    app.config.from_object(config[config_name])
    
    # 配置日志
    setup_logging(app)
    
    # 初始化扩展
    init_extensions(app)
    
    # 注册蓝图
    register_blueprints(app)
    
    # 注册错误处理器
    register_error_handlers(app)
    
    # 应用初始化
    with app.app_context():
        init_application(app)
    
    return app

def setup_logging(app):
    """配置日志系统"""
    log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
    log_file = os.getenv('LOG_FILE', 'logs/app.log')
    
    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 配置日志格式
    logging.basicConfig(
        level=getattr(logging, log_level),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file, encoding='utf-8')
        ]
    )
    
    logger = logging.getLogger(__name__)
    logger.info("✅ 日志系统初始化完成")

def init_extensions(app):
    """初始化Flask扩展"""
    # 初始化压缩
    try:
        from flask_compress import Compress
        Compress(app)
        logging.getLogger(__name__).info("✅ Flask压缩已启用")
    except ImportError:
        logging.getLogger(__name__).warning("⚠️ Flask-Compress未安装")
    
    # 初始化缓存服务
    from app.services.cache_service import cache_service
    app.cache_service = cache_service
    logging.getLogger(__name__).info("✅ 缓存服务已初始化")
    
    # 初始化数据库服务
    from app.services.database_service import db_service
    app.db_service = db_service
    logging.getLogger(__name__).info("✅ 数据库服务已初始化")

def register_blueprints(app):
    """注册蓝图"""
    
    # 注册认证蓝图
    from app.web.auth import auth_bp
    app.register_blueprint(auth_bp)
    
    # 注册主页面蓝图
    from app.web.main import main_bp
    app.register_blueprint(main_bp)
    
    # 注册API蓝图
    from app.api.dashboard import dashboard_api
    app.register_blueprint(dashboard_api, url_prefix='/api')
    
    from app.api.inventory import inventory_api
    app.register_blueprint(inventory_api, url_prefix='/api')
    
    from app.api.tracking import tracking_api
    app.register_blueprint(tracking_api, url_prefix='/api')
    
    from app.api.cache import cache_api
    app.register_blueprint(cache_api, url_prefix='/api')

    from app.api.recommendations import recommendations_api
    app.register_blueprint(recommendations_api, url_prefix='/api')

    logging.getLogger(__name__).info("✅ 蓝图注册完成")

def register_error_handlers(app):
    """注册错误处理器"""
    from app.utils.error_handlers import register_error_handlers as reg_handlers
    reg_handlers(app)
    logging.getLogger(__name__).info("✅ 错误处理器注册完成")

def init_application(app):
    """应用初始化"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🚀 启动KKUGUAN库存管理系统 - 模块化架构")
        
        # 检查数据库连接
        from database_config import DatabaseConfig
        db_config = DatabaseConfig()
        
        # 更新数据库表结构
        logger.info("📝 检查并更新数据库表结构...")
        try:
            db_config.update_table_structure()
            logger.info("✅ 数据库表结构检查完成")
        except Exception as e:
            logger.warning(f"⚠️ 数据库表结构更新失败: {e}")
        
        # 检查飞书API
        try:
            from feishu_config import FEISHU_ENABLED
            if FEISHU_ENABLED:
                logger.info("✅ 飞书API集成已启用")
            else:
                logger.info("⚠️ 飞书API集成未启用")
        except ImportError:
            logger.info("⚠️ 飞书API配置未找到")
        
        # 启动缓存预热
        from app.services.warmup_service import start_warmup
        start_warmup()
        
        logger.info("✅ 应用初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 应用初始化失败: {e}")
        return False
