"""
缓存预热服务
应用启动时预加载关键数据
"""

import time
import threading
import logging
from typing import Dict, Callable

logger = logging.getLogger(__name__)

class CacheWarmupService:
    """缓存预热服务"""
    
    def __init__(self):
        self.warmup_functions = {}
        self.warmup_progress = {}
        self.is_warming_up = False
    
    def register_warmup_function(self, name: str, func: Callable, priority: int = 1):
        """注册预热函数"""
        self.warmup_functions[name] = {
            'function': func,
            'priority': priority,
            'status': 'pending'
        }
        logger.info(f"注册预热函数: {name} (优先级: {priority})")
    
    def start_warmup(self):
        """启动缓存预热"""
        if self.is_warming_up:
            logger.warning("预热已在进行中")
            return False
        
        self.is_warming_up = True
        logger.info("🔥 开始缓存预热...")
        
        try:
            # 按优先级排序
            sorted_functions = sorted(
                self.warmup_functions.items(),
                key=lambda x: x[1]['priority'],
                reverse=True
            )
            
            total_functions = len(sorted_functions)
            completed = 0
            
            for name, config in sorted_functions:
                try:
                    logger.info(f"🔥 预热: {name}")
                    start_time = time.time()
                    
                    # 执行预热函数
                    config['function']()
                    
                    execution_time = time.time() - start_time
                    config['status'] = 'completed'
                    completed += 1
                    
                    progress = (completed / total_functions) * 100
                    self.warmup_progress[name] = {
                        'status': 'completed',
                        'execution_time': execution_time,
                        'progress': progress
                    }
                    
                    logger.info(f"✅ 预热完成: {name} - 耗时 {execution_time:.2f}秒 ({progress:.1f}%)")
                    
                except Exception as e:
                    logger.error(f"❌ 预热失败: {name} - {e}")
                    config['status'] = 'failed'
                    self.warmup_progress[name] = {
                        'status': 'failed',
                        'error': str(e),
                        'progress': (completed / total_functions) * 100
                    }
            
            logger.info("🎉 缓存预热完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 缓存预热失败: {e}")
            return False
        
        finally:
            self.is_warming_up = False
    
    def get_warmup_status(self):
        """获取预热状态"""
        return {
            'is_warming_up': self.is_warming_up,
            'progress': self.warmup_progress,
            'total_functions': len(self.warmup_functions),
            'completed_functions': len([p for p in self.warmup_progress.values() if p['status'] == 'completed'])
        }

# 全局预热服务实例
warmup_service = CacheWarmupService()

def register_default_warmup_functions():
    """注册默认的预热函数"""
    
    def warmup_dashboard_stats():
        """预热仪表板统计数据"""
        try:
            from database_config import get_dashboard_stats
            from flask import current_app
            
            cache_service = current_app.cache_service
            cache_service.get_or_set('dashboard_stats', get_dashboard_stats, ttl=300)
            
        except Exception as e:
            logger.error(f"预热仪表板统计失败: {e}")
    
    def warmup_sku_list():
        """预热SKU列表"""
        try:
            from database_config import get_sku_list
            from flask import current_app
            
            cache_service = current_app.cache_service
            cache_service.get_or_set('sku_list_all', get_sku_list, ttl=1800)
            
        except Exception as e:
            logger.error(f"预热SKU列表失败: {e}")
    
    def warmup_inventory_alerts():
        """预热库存提醒"""
        try:
            from database_config import get_inventory_alerts
            from datetime import datetime, timedelta
            from flask import current_app
            
            cache_service = current_app.cache_service
            
            # 预热最近7天的库存提醒
            for i in range(7):
                date = (datetime.now() - timedelta(days=i)).strftime('%Y-%m-%d')
                cache_key = f"inventory_alerts_daily_{date}"
                cache_service.get_or_set(
                    cache_key,
                    lambda d=date: get_inventory_alerts(d, d),
                    ttl=3600
                )
            
        except Exception as e:
            logger.error(f"预热库存提醒失败: {e}")
    
    def warmup_restock_calendar():
        """预热补货日历"""
        try:
            from database_config import get_restock_calendar
            from flask import current_app
            
            cache_service = current_app.cache_service
            cache_service.get_or_set('restock_calendar', get_restock_calendar, ttl=3600)
            
        except Exception as e:
            logger.error(f"预热补货日历失败: {e}")
    
    # 注册预热函数（按优先级）
    warmup_service.register_warmup_function('dashboard_stats', warmup_dashboard_stats, priority=5)
    warmup_service.register_warmup_function('sku_list', warmup_sku_list, priority=4)
    warmup_service.register_warmup_function('inventory_alerts', warmup_inventory_alerts, priority=3)
    warmup_service.register_warmup_function('restock_calendar', warmup_restock_calendar, priority=2)

def start_warmup():
    """启动预热（异步）"""
    def warmup_task():
        try:
            # 注册默认预热函数
            register_default_warmup_functions()
            
            # 启动预热
            success = warmup_service.start_warmup()
            
            if success:
                logger.info("✅ 应用预热成功，系统就绪")
            else:
                logger.error("❌ 应用预热失败")
                
        except Exception as e:
            logger.error(f"❌ 预热任务异常: {e}")
    
    # 在后台线程中执行预热
    warmup_thread = threading.Thread(target=warmup_task, daemon=True)
    warmup_thread.start()
    logger.info("🔥 预热任务已启动")

def get_warmup_status():
    """获取预热状态"""
    return warmup_service.get_warmup_status()
