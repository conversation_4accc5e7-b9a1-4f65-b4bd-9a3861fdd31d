"""
优化的物流追踪服务
使用连接池、缓存和批量操作
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from app.services.database_service import db_service
from app.services.cache_service import cache_service, cached

logger = logging.getLogger(__name__)

class OptimizedTrackingService:
    """优化的物流追踪服务"""
    
    def __init__(self):
        self.db = db_service
        self.cache = cache_service
    
    @cached(key_pattern="tracking_data_all", ttl=180)  # 3分钟缓存
    def get_all_tracking_data(self) -> List[Dict[str, Any]]:
        """获取所有物流追踪数据（优化版）"""
        query = """
        SELECT 
            order_number,
            tracking_number,
            platform_channel as platform,
            store_account as shop,
            order_date,
            status,
            latest_event_description,
            latest_event_location,
            latest_event_time,
            remark,
            is_uninvited_review,
            full_track_info,
            updated_at
        FROM orders 
        WHERE tracking_number IS NOT NULL 
        AND tracking_number != ''
        ORDER BY order_date DESC
        """
        
        try:
            results = self.db.execute_query(
                query, 
                cache_key="tracking_data_query",
                cache_ttl=180
            )
            
            # 处理数据格式
            processed_data = []
            for item in results:
                # 处理JSON字段
                full_track_info = item.get('full_track_info')
                if isinstance(full_track_info, str):
                    try:
                        full_track_info = json.loads(full_track_info)
                    except:
                        full_track_info = {}
                
                processed_item = {
                    'order_number': item.get('order_number', ''),
                    'tracking_number': item.get('tracking_number', ''),
                    'platform': item.get('platform', ''),
                    'shop': item.get('shop', ''),
                    'order_date': item.get('order_date'),
                    'status': item.get('status', 'No Tracking'),
                    'latest_event_description': item.get('latest_event_description', ''),
                    'latest_event_location': item.get('latest_event_location', ''),
                    'latest_event_time': item.get('latest_event_time'),
                    'remark': item.get('remark', ''),
                    'is_uninvited_review': bool(item.get('is_uninvited_review', False)),
                    'full_track_info': full_track_info,
                    'updated_at': item.get('updated_at')
                }
                processed_data.append(processed_item)
            
            logger.info(f"获取物流数据成功: {len(processed_data)} 条记录")
            return processed_data
            
        except Exception as e:
            logger.error(f"获取物流数据失败: {e}")
            raise
    
    def get_tracking_by_number(self, tracking_number: str) -> Optional[Dict[str, Any]]:
        """根据跟踪单号获取物流信息"""
        cache_key = f"tracking_single_{tracking_number}"
        
        # 先检查缓存
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data
        
        query = """
        SELECT 
            order_number,
            tracking_number,
            platform_channel as platform,
            store_account as shop,
            order_date,
            status,
            latest_event_description,
            latest_event_location,
            latest_event_time,
            remark,
            is_uninvited_review,
            full_track_info,
            updated_at
        FROM orders 
        WHERE tracking_number = %s
        """
        
        try:
            results = self.db.execute_query(query, (tracking_number,))
            if results:
                data = results[0]
                
                # 处理JSON字段
                full_track_info = data.get('full_track_info')
                if isinstance(full_track_info, str):
                    try:
                        full_track_info = json.loads(full_track_info)
                    except:
                        full_track_info = {}
                
                processed_data = {
                    'order_number': data.get('order_number', ''),
                    'tracking_number': data.get('tracking_number', ''),
                    'platform': data.get('platform', ''),
                    'shop': data.get('shop', ''),
                    'order_date': data.get('order_date'),
                    'status': data.get('status', 'No Tracking'),
                    'latest_event_description': data.get('latest_event_description', ''),
                    'latest_event_location': data.get('latest_event_location', ''),
                    'latest_event_time': data.get('latest_event_time'),
                    'remark': data.get('remark', ''),
                    'is_uninvited_review': bool(data.get('is_uninvited_review', False)),
                    'full_track_info': full_track_info,
                    'updated_at': data.get('updated_at')
                }
                
                # 缓存结果
                self.cache.set(cache_key, processed_data, ttl=300)  # 5分钟缓存
                return processed_data
            
            return None
            
        except Exception as e:
            logger.error(f"获取单个物流信息失败: {tracking_number} - {e}")
            raise
    
    def update_tracking_data(self, order_number: str, tracking_number: str, 
                           remark: str = '', is_uninvited_review: bool = False) -> bool:
        """更新物流数据（优化版）"""
        try:
            # 更新数据库
            update_query = """
            UPDATE orders 
            SET tracking_number = %s, 
                remark = %s, 
                is_uninvited_review = %s, 
                updated_at = CURRENT_TIMESTAMP
            WHERE order_number = %s
            """
            
            affected_rows = self.db.execute_update(
                update_query, 
                (tracking_number, remark, is_uninvited_review, order_number)
            )
            
            if affected_rows > 0:
                # 清除相关缓存
                self._clear_related_cache(order_number, tracking_number)
                logger.info(f"更新物流数据成功: {order_number}")
                return True
            else:
                logger.warning(f"更新物流数据失败，未找到订单: {order_number}")
                return False
                
        except Exception as e:
            logger.error(f"更新物流数据失败: {order_number} - {e}")
            raise
    
    def batch_update_review_status(self, order_number: str, is_uninvited_review: bool) -> bool:
        """批量更新订单下所有包裹的邀评状态"""
        try:
            # 批量更新同一订单下的所有记录
            update_query = """
            UPDATE orders 
            SET is_uninvited_review = %s, 
                updated_at = CURRENT_TIMESTAMP
            WHERE order_number = %s
            """
            
            affected_rows = self.db.execute_update(
                update_query, 
                (is_uninvited_review, order_number)
            )
            
            if affected_rows > 0:
                # 清除相关缓存
                self._clear_related_cache(order_number)
                logger.info(f"批量更新邀评状态成功: {order_number} - 影响 {affected_rows} 条记录")
                return True
            else:
                logger.warning(f"批量更新邀评状态失败，未找到订单: {order_number}")
                return False
                
        except Exception as e:
            logger.error(f"批量更新邀评状态失败: {order_number} - {e}")
            raise
    
    def get_tracking_statistics(self) -> Dict[str, Any]:
        """获取物流统计信息（缓存版）"""
        cache_key = "tracking_statistics"
        
        def get_stats():
            query = """
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN tracking_number IS NOT NULL AND tracking_number != '' THEN 1 END) as with_tracking,
                COUNT(CASE WHEN is_uninvited_review = 1 THEN 1 END) as reviewed,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered,
                COUNT(CASE WHEN status IN ('In Transit', 'Out for Delivery') THEN 1 END) as in_transit
            FROM orders
            WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            """
            
            result = self.db.execute_query(query)
            if result:
                stats = result[0]
                return {
                    'total_orders': stats['total_orders'],
                    'with_tracking': stats['with_tracking'],
                    'tracking_rate': round((stats['with_tracking'] / stats['total_orders']) * 100, 2) if stats['total_orders'] > 0 else 0,
                    'reviewed': stats['reviewed'],
                    'review_rate': round((stats['reviewed'] / stats['with_tracking']) * 100, 2) if stats['with_tracking'] > 0 else 0,
                    'delivered': stats['delivered'],
                    'in_transit': stats['in_transit']
                }
            return {}
        
        return self.cache.get_or_set(cache_key, get_stats, ttl=600)  # 10分钟缓存
    
    def _clear_related_cache(self, order_number: str, tracking_number: str = None):
        """清除相关缓存"""
        # 清除全局缓存
        self.cache.delete("tracking_data_all")
        self.cache.delete("tracking_data_query")
        self.cache.delete("tracking_statistics")
        
        # 清除单个跟踪号缓存
        if tracking_number:
            self.cache.delete(f"tracking_single_{tracking_number}")
        
        # 清除模式匹配的缓存
        self.cache.clear_pattern("tracking_")
        
        logger.debug(f"清除物流相关缓存: {order_number}")

# 全局物流服务实例
tracking_service = OptimizedTrackingService()
