"""
优化的数据库服务
使用连接池和查询优化
"""

import mysql.connector.pooling
import logging
import time
import threading
from contextlib import contextmanager
from typing import List, Dict, Any, Optional
import os
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class OptimizedDatabaseService:
    """优化的数据库服务"""
    
    def __init__(self):
        self.connection_pools = {}
        self.query_cache = {}
        self.cache_lock = threading.Lock()
        self.cache_ttl = 300  # 5分钟缓存
        self._init_connection_pools()
    
    def _init_connection_pools(self):
        """初始化连接池"""
        # 主数据库连接池（本地）
        primary_config = {
            'host': os.getenv('DB_HOST_PRIMARY', '127.0.0.1'),
            'user': os.getenv('DB_USER', 'Seller'),
            'password': os.getenv('DB_PASSWORD', '98c06z27W@'),
            'database': os.getenv('DB_NAME', 'kkuguan_db'),
            'charset': 'utf8mb4',
            'port': int(os.getenv('DB_PORT', '3306')),
            'pool_name': 'primary_pool',
            'pool_size': 10,  # 连接池大小
            'pool_reset_session': True,
            'autocommit': True,
            'connection_timeout': 5,  # 本地连接超时短
            'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
        }
        
        # 备用数据库连接池（远程）
        fallback_config = {
            'host': os.getenv('DB_HOST_FALLBACK', '*************'),
            'user': os.getenv('DB_USER', 'Seller'),
            'password': os.getenv('DB_PASSWORD', '98c06z27W@'),
            'database': os.getenv('DB_NAME', 'kkuguan_db'),
            'charset': 'utf8mb4',
            'port': int(os.getenv('DB_PORT', '3306')),
            'pool_name': 'fallback_pool',
            'pool_size': 5,  # 远程连接池较小
            'pool_reset_session': True,
            'autocommit': True,
            'connection_timeout': 30,  # 远程连接超时长
            'sql_mode': 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'
        }
        
        # 尝试创建主连接池
        try:
            self.connection_pools['primary'] = mysql.connector.pooling.MySQLConnectionPool(**primary_config)
            self.current_pool = 'primary'
            logger.info("✅ 主数据库连接池创建成功（本地）")
        except Exception as e:
            logger.warning(f"⚠️ 主数据库连接池创建失败: {e}")
            try:
                self.connection_pools['fallback'] = mysql.connector.pooling.MySQLConnectionPool(**fallback_config)
                self.current_pool = 'fallback'
                logger.info("✅ 备用数据库连接池创建成功（远程）")
            except Exception as e2:
                logger.error(f"❌ 备用数据库连接池也创建失败: {e2}")
                raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        connection = None
        try:
            # 优先使用主连接池
            if 'primary' in self.connection_pools:
                try:
                    connection = self.connection_pools['primary'].get_connection()
                    yield connection
                    return
                except Exception as e:
                    logger.warning(f"主连接池获取连接失败: {e}")
            
            # 使用备用连接池
            if 'fallback' in self.connection_pools:
                connection = self.connection_pools['fallback'].get_connection()
                yield connection
            else:
                raise Exception("没有可用的数据库连接池")
                
        finally:
            if connection and connection.is_connected():
                connection.close()
    
    def execute_query(self, query: str, params: tuple = None, cache_key: str = None, cache_ttl: int = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        start_time = time.time()
        
        # 检查缓存
        if cache_key:
            cached_result = self._get_cached_result(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
        
        try:
            with self.get_connection() as connection:
                cursor = connection.cursor(dictionary=True, buffered=True)
                cursor.execute(query, params or ())
                result = cursor.fetchall()
                cursor.close()
                
                # 缓存结果
                if cache_key:
                    self._cache_result(cache_key, result, cache_ttl or self.cache_ttl)
                
                execution_time = (time.time() - start_time) * 1000
                logger.debug(f"查询执行时间: {execution_time:.2f}ms, 返回 {len(result)} 行")
                
                # 记录慢查询
                if execution_time > 1000:
                    logger.warning(f"慢查询检测: {execution_time:.2f}ms - {query[:100]}...")
                
                return result
                
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"查询执行失败 ({execution_time:.2f}ms): {e}")
            raise
    
    def execute_update(self, query: str, params: tuple = None) -> int:
        """执行更新操作并返回影响的行数"""
        start_time = time.time()
        
        try:
            with self.get_connection() as connection:
                cursor = connection.cursor()
                cursor.execute(query, params or ())
                affected_rows = cursor.rowcount
                cursor.close()
                
                execution_time = (time.time() - start_time) * 1000
                logger.debug(f"更新执行时间: {execution_time:.2f}ms, 影响 {affected_rows} 行")
                
                return affected_rows
                
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"更新执行失败 ({execution_time:.2f}ms): {e}")
            raise
    
    def execute_batch(self, query: str, params_list: List[tuple]) -> int:
        """批量执行操作"""
        start_time = time.time()
        
        try:
            with self.get_connection() as connection:
                cursor = connection.cursor()
                cursor.executemany(query, params_list)
                affected_rows = cursor.rowcount
                cursor.close()
                
                execution_time = (time.time() - start_time) * 1000
                logger.info(f"批量操作执行时间: {execution_time:.2f}ms, 影响 {affected_rows} 行")
                
                return affected_rows
                
        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"批量操作执行失败 ({execution_time:.2f}ms): {e}")
            raise
    
    def _get_cached_result(self, cache_key: str) -> Optional[List[Dict[str, Any]]]:
        """获取缓存结果"""
        with self.cache_lock:
            if cache_key in self.query_cache:
                cached_data, timestamp = self.query_cache[cache_key]
                if time.time() - timestamp < self.cache_ttl:
                    return cached_data
                else:
                    del self.query_cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: List[Dict[str, Any]], ttl: int):
        """缓存查询结果"""
        with self.cache_lock:
            self.query_cache[cache_key] = (result, time.time())
            # 清理过期缓存
            current_time = time.time()
            expired_keys = [
                key for key, (_, timestamp) in self.query_cache.items()
                if current_time - timestamp > ttl
            ]
            for key in expired_keys:
                del self.query_cache[key]
    
    def clear_cache(self, pattern: str = None):
        """清理缓存"""
        with self.cache_lock:
            if pattern:
                keys_to_remove = [key for key in self.query_cache.keys() if pattern in key]
                for key in keys_to_remove:
                    del self.query_cache[key]
            else:
                self.query_cache.clear()
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        status = {}
        for pool_name, pool in self.connection_pools.items():
            try:
                status[pool_name] = {
                    'pool_size': pool.pool_size,
                    'pool_name': pool.pool_name,
                    'active': True
                }
            except Exception as e:
                status[pool_name] = {
                    'active': False,
                    'error': str(e)
                }
        
        status['cache_size'] = len(self.query_cache)
        status['current_pool'] = self.current_pool
        return status

# 全局数据库服务实例
db_service = OptimizedDatabaseService()
