"""
优化的缓存服务
多层缓存策略：Redis + 内存缓存 + 查询缓存
"""

import redis
import pickle
import json
import time
import threading
import logging
from typing import Any, Optional, Dict, Callable
from datetime import datetime, timedelta
import os
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class OptimizedCacheService:
    """优化的缓存服务"""
    
    def __init__(self):
        self.redis_client = None
        self.memory_cache = {}
        self.cache_stats = {
            'redis_hits': 0,
            'memory_hits': 0,
            'misses': 0,
            'sets': 0
        }
        self.lock = threading.Lock()
        self._init_redis()
        
        # 缓存策略配置
        self.cache_strategies = {
            'dashboard_stats': {'ttl': 300, 'memory_ttl': 60},      # 5分钟Redis，1分钟内存
            'inventory_alerts': {'ttl': 600, 'memory_ttl': 120},    # 10分钟Redis，2分钟内存
            'tracking_data': {'ttl': 180, 'memory_ttl': 30},        # 3分钟Redis，30秒内存
            'sku_list': {'ttl': 1800, 'memory_ttl': 300},           # 30分钟Redis，5分钟内存
            'restock_calendar': {'ttl': 3600, 'memory_ttl': 600},   # 1小时Redis，10分钟内存
            'default': {'ttl': 300, 'memory_ttl': 60}               # 默认策略
        }
    
    def _init_redis(self):
        """初始化Redis连接"""
        try:
            redis_host = os.getenv('REDIS_HOST', 'localhost')
            redis_port = int(os.getenv('REDIS_PORT', '6379'))
            redis_password = os.getenv('REDIS_PASSWORD', '')
            
            self.redis_client = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password if redis_password else None,
                decode_responses=False,  # 保持二进制模式以支持pickle
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            
            # 测试连接
            self.redis_client.ping()
            logger.info(f"✅ Redis连接成功: {redis_host}:{redis_port}")
            
        except Exception as e:
            logger.warning(f"⚠️ Redis连接失败，使用内存缓存: {e}")
            self.redis_client = None
    
    def _get_strategy(self, cache_key: str) -> Dict[str, int]:
        """获取缓存策略"""
        for pattern, strategy in self.cache_strategies.items():
            if pattern in cache_key or pattern == 'default':
                return strategy
        return self.cache_strategies['default']
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        strategy = self._get_strategy(key)
        
        # 1. 先检查内存缓存
        with self.lock:
            if key in self.memory_cache:
                data, timestamp = self.memory_cache[key]
                if time.time() - timestamp < strategy['memory_ttl']:
                    self.cache_stats['memory_hits'] += 1
                    logger.debug(f"内存缓存命中: {key}")
                    return data
                else:
                    del self.memory_cache[key]
        
        # 2. 检查Redis缓存
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(key)
                if cached_data:
                    data = pickle.loads(cached_data)
                    
                    # 更新内存缓存
                    with self.lock:
                        self.memory_cache[key] = (data, time.time())
                        self.cache_stats['redis_hits'] += 1
                    
                    logger.debug(f"Redis缓存命中: {key}")
                    return data
            except Exception as e:
                logger.warning(f"Redis获取失败: {key} - {e}")
        
        # 3. 缓存未命中
        with self.lock:
            self.cache_stats['misses'] += 1
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        strategy = self._get_strategy(key)
        cache_ttl = ttl or strategy['ttl']
        
        try:
            # 1. 设置Redis缓存
            if self.redis_client:
                try:
                    serialized_data = pickle.dumps(value)
                    self.redis_client.setex(key, cache_ttl, serialized_data)
                except Exception as e:
                    logger.warning(f"Redis设置失败: {key} - {e}")
            
            # 2. 设置内存缓存
            with self.lock:
                self.memory_cache[key] = (value, time.time())
                self.cache_stats['sets'] += 1
            
            logger.debug(f"缓存设置成功: {key} (TTL: {cache_ttl}s)")
            return True
            
        except Exception as e:
            logger.error(f"缓存设置失败: {key} - {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        try:
            # 删除Redis缓存
            if self.redis_client:
                try:
                    self.redis_client.delete(key)
                except Exception as e:
                    logger.warning(f"Redis删除失败: {key} - {e}")
            
            # 删除内存缓存
            with self.lock:
                self.memory_cache.pop(key, None)
            
            logger.debug(f"缓存删除成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"缓存删除失败: {key} - {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """清除匹配模式的缓存"""
        cleared_count = 0
        
        try:
            # 清除Redis缓存
            if self.redis_client:
                try:
                    keys = self.redis_client.keys(f"*{pattern}*")
                    if keys:
                        self.redis_client.delete(*keys)
                        cleared_count += len(keys)
                except Exception as e:
                    logger.warning(f"Redis模式清除失败: {pattern} - {e}")
            
            # 清除内存缓存
            with self.lock:
                keys_to_remove = [key for key in self.memory_cache.keys() if pattern in key]
                for key in keys_to_remove:
                    del self.memory_cache[key]
                cleared_count += len(keys_to_remove)
            
            logger.info(f"模式清除完成: {pattern} - 清除 {cleared_count} 个缓存")
            return cleared_count
            
        except Exception as e:
            logger.error(f"模式清除失败: {pattern} - {e}")
            return 0
    
    def get_or_set(self, key: str, func: Callable, ttl: Optional[int] = None, *args, **kwargs) -> Any:
        """获取缓存或执行函数并缓存结果"""
        # 尝试获取缓存
        cached_value = self.get(key)
        if cached_value is not None:
            return cached_value
        
        # 执行函数获取数据
        start_time = time.time()
        try:
            value = func(*args, **kwargs)
            execution_time = (time.time() - start_time) * 1000
            
            # 缓存结果
            self.set(key, value, ttl)
            
            logger.debug(f"函数执行并缓存: {key} - 耗时 {execution_time:.2f}ms")
            return value
            
        except Exception as e:
            logger.error(f"函数执行失败: {key} - {e}")
            raise
    
    def cleanup_expired(self):
        """清理过期的内存缓存"""
        current_time = time.time()
        expired_keys = []
        
        with self.lock:
            for key, (data, timestamp) in self.memory_cache.items():
                strategy = self._get_strategy(key)
                if current_time - timestamp > strategy['memory_ttl']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.memory_cache[key]
        
        if expired_keys:
            logger.debug(f"清理过期内存缓存: {len(expired_keys)} 个")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total_requests = sum(self.cache_stats.values()) - self.cache_stats['sets']
            hit_rate = 0
            if total_requests > 0:
                hits = self.cache_stats['redis_hits'] + self.cache_stats['memory_hits']
                hit_rate = (hits / total_requests) * 100
            
            return {
                'cache_stats': self.cache_stats.copy(),
                'hit_rate': round(hit_rate, 2),
                'memory_cache_size': len(self.memory_cache),
                'redis_available': self.redis_client is not None,
                'strategies': self.cache_strategies
            }
    
    def warm_up(self, warm_up_functions: Dict[str, Callable]):
        """缓存预热"""
        logger.info("🔥 开始缓存预热...")
        
        for cache_key, func in warm_up_functions.items():
            try:
                start_time = time.time()
                self.get_or_set(cache_key, func)
                execution_time = (time.time() - start_time) * 1000
                logger.info(f"✅ 预热完成: {cache_key} - 耗时 {execution_time:.2f}ms")
            except Exception as e:
                logger.error(f"❌ 预热失败: {cache_key} - {e}")
        
        logger.info("🎉 缓存预热完成")

# 全局缓存服务实例
cache_service = OptimizedCacheService()

def cached(key_pattern: str = None, ttl: int = None):
    """缓存装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_pattern:
                cache_key = key_pattern.format(*args, **kwargs)
            else:
                cache_key = f"{func.__name__}_{hash(str(args) + str(kwargs))}"
            
            return cache_service.get_or_set(cache_key, func, ttl, *args, **kwargs)
        return wrapper
    return decorator
