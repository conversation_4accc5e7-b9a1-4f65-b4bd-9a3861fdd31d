"""
装饰器工具模块
提供常用的装饰器函数
"""

import time
import hashlib
import traceback
import logging
from functools import wraps
from flask import request, jsonify

logger = logging.getLogger(__name__)

def handle_errors(f):
    """全局错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            error_id = hashlib.md5(f"{time.time()}{str(e)}".encode()).hexdigest()[:8]
            logger.error(f"错误ID: {error_id} - 函数: {f.__name__} - 错误: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")

            # 返回统一的错误响应
            if request.path.startswith('/api/'):
                return jsonify({
                    'success': False,
                    'error': '服务器内部错误',
                    'error_id': error_id,
                    'timestamp': time.time()
                }), 500
            else:
                # 对于页面请求，可以重定向到错误页面
                from flask import render_template
                return render_template('error.html', 
                                     error_message='服务器内部错误',
                                     error_id=error_id), 500
    return decorated_function

def monitor_performance(f):
    """性能监控装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        endpoint = f.__name__
        
        try:
            result = f(*args, **kwargs)
            status_code = 200
            
            # 尝试从Flask响应中获取状态码
            if hasattr(result, 'status_code'):
                status_code = result.status_code
            elif isinstance(result, tuple) and len(result) > 1:
                status_code = result[1]
                
            return result
            
        except Exception as e:
            status_code = 500
            raise
            
        finally:
            duration = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 记录性能数据
            try:
                from app.utils.performance_monitor import performance_monitor
                performance_monitor.record_api_call(endpoint, duration, status_code)
            except ImportError:
                pass
            
            # 记录慢请求
            if duration > 2000:  # 2秒
                logger.warning(f"慢请求检测: {endpoint} 耗时 {duration:.0f}ms")
            
            logger.debug(f"API性能: {endpoint} - {duration:.2f}ms - {status_code}")
    
    return decorated_function

def cache_result(cache_key_pattern=None, ttl=300):
    """结果缓存装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 生成缓存键
            if cache_key_pattern:
                cache_key = cache_key_pattern.format(*args, **kwargs)
            else:
                cache_key = f"{f.__name__}_{hash(str(args) + str(kwargs))}"
            
            try:
                from flask import current_app
                cache_service = current_app.cache_service
                
                # 尝试从缓存获取
                cached_result = cache_service.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"缓存命中: {cache_key}")
                    return cached_result
                
                # 执行函数并缓存结果
                result = f(*args, **kwargs)
                cache_service.set(cache_key, result, ttl)
                logger.debug(f"缓存设置: {cache_key}")
                
                return result
                
            except Exception as e:
                logger.warning(f"缓存操作失败: {e}")
                # 缓存失败时直接执行函数
                return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def validate_json(required_fields=None):
    """JSON数据验证装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not request.is_json:
                return jsonify({
                    'success': False,
                    'error': '请求必须是JSON格式'
                }), 400
            
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'error': '请求体不能为空'
                }), 400
            
            # 检查必需字段
            if required_fields:
                missing_fields = [field for field in required_fields if field not in data]
                if missing_fields:
                    return jsonify({
                        'success': False,
                        'error': f'缺少必需字段: {", ".join(missing_fields)}'
                    }), 400
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def rate_limit(max_requests=100, window=3600):
    """简单的速率限制装饰器"""
    request_counts = {}
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.remote_addr
            current_time = time.time()
            
            # 清理过期记录
            expired_ips = [ip for ip, (count, timestamp) in request_counts.items() 
                          if current_time - timestamp > window]
            for ip in expired_ips:
                del request_counts[ip]
            
            # 检查当前IP的请求次数
            if client_ip in request_counts:
                count, timestamp = request_counts[client_ip]
                if current_time - timestamp < window:
                    if count >= max_requests:
                        return jsonify({
                            'success': False,
                            'error': '请求过于频繁，请稍后再试'
                        }), 429
                    request_counts[client_ip] = (count + 1, timestamp)
                else:
                    request_counts[client_ip] = (1, current_time)
            else:
                request_counts[client_ip] = (1, current_time)
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def require_api_key(f):
    """API密钥验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('X-API-Key') or request.args.get('api_key')
        
        if not api_key:
            return jsonify({
                'success': False,
                'error': '缺少API密钥'
            }), 401
        
        # 这里可以添加API密钥验证逻辑
        # 目前简单检查是否存在
        valid_api_keys = ['your-api-key-here']  # 应该从配置中读取
        
        if api_key not in valid_api_keys:
            return jsonify({
                'success': False,
                'error': '无效的API密钥'
            }), 401
        
        return f(*args, **kwargs)
    
    return decorated_function
