"""
性能监控工具
用于监控API响应时间、数据库查询性能等
"""

import time
import logging
from functools import wraps
from datetime import datetime
import threading
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_records=1000):
        self.max_records = max_records
        self.api_metrics = defaultdict(lambda: deque(maxlen=max_records))
        self.db_metrics = defaultdict(lambda: deque(maxlen=max_records))
        self.lock = threading.Lock()
    
    def record_api_call(self, endpoint, duration, status_code):
        """记录API调用性能"""
        with self.lock:
            self.api_metrics[endpoint].append({
                'timestamp': datetime.utcnow(),
                'duration': duration,
                'status_code': status_code
            })
    
    def record_db_query(self, query_type, duration, rows_affected=0):
        """记录数据库查询性能"""
        with self.lock:
            self.db_metrics[query_type].append({
                'timestamp': datetime.utcnow(),
                'duration': duration,
                'rows_affected': rows_affected
            })
    
    def get_api_stats(self, endpoint=None, minutes=60):
        """获取API性能统计"""
        with self.lock:
            if endpoint:
                metrics = [self.api_metrics[endpoint]]
                endpoints = [endpoint]
            else:
                metrics = list(self.api_metrics.values())
                endpoints = list(self.api_metrics.keys())
            
            stats = {}
            cutoff_time = datetime.utcnow().timestamp() - (minutes * 60)
            
            for i, endpoint_metrics in enumerate(metrics):
                endpoint_name = endpoints[i]
                recent_calls = [
                    call for call in endpoint_metrics 
                    if call['timestamp'].timestamp() > cutoff_time
                ]
                
                if recent_calls:
                    durations = [call['duration'] for call in recent_calls]
                    error_count = sum(1 for call in recent_calls if call['status_code'] >= 400)
                    
                    stats[endpoint_name] = {
                        'total_calls': len(recent_calls),
                        'avg_duration': sum(durations) / len(durations),
                        'min_duration': min(durations),
                        'max_duration': max(durations),
                        'error_rate': error_count / len(recent_calls),
                        'calls_per_minute': len(recent_calls) / minutes
                    }
            
            return stats
    
    def get_db_stats(self, query_type=None, minutes=60):
        """获取数据库性能统计"""
        with self.lock:
            if query_type:
                metrics = [self.db_metrics[query_type]]
                types = [query_type]
            else:
                metrics = list(self.db_metrics.values())
                types = list(self.db_metrics.keys())
            
            stats = {}
            cutoff_time = datetime.utcnow().timestamp() - (minutes * 60)
            
            for i, type_metrics in enumerate(metrics):
                type_name = types[i]
                recent_queries = [
                    query for query in type_metrics 
                    if query['timestamp'].timestamp() > cutoff_time
                ]
                
                if recent_queries:
                    durations = [query['duration'] for query in recent_queries]
                    total_rows = sum(query['rows_affected'] for query in recent_queries)
                    
                    stats[type_name] = {
                        'total_queries': len(recent_queries),
                        'avg_duration': sum(durations) / len(durations),
                        'min_duration': min(durations),
                        'max_duration': max(durations),
                        'total_rows_affected': total_rows,
                        'queries_per_minute': len(recent_queries) / minutes
                    }
            
            return stats
    
    def get_health_status(self):
        """获取系统健康状态"""
        api_stats = self.get_api_stats(minutes=10)  # 最近10分钟
        db_stats = self.get_db_stats(minutes=10)
        
        # 计算整体健康分数
        health_score = 100
        issues = []
        
        # 检查API性能
        for endpoint, stats in api_stats.items():
            if stats['avg_duration'] > 2000:  # 2秒
                health_score -= 10
                issues.append(f"API {endpoint} 响应时间过长: {stats['avg_duration']:.0f}ms")
            
            if stats['error_rate'] > 0.05:  # 5%
                health_score -= 15
                issues.append(f"API {endpoint} 错误率过高: {stats['error_rate']:.1%}")
        
        # 检查数据库性能
        for query_type, stats in db_stats.items():
            if stats['avg_duration'] > 1000:  # 1秒
                health_score -= 10
                issues.append(f"数据库查询 {query_type} 响应时间过长: {stats['avg_duration']:.0f}ms")
        
        return {
            'health_score': max(0, health_score),
            'status': 'healthy' if health_score >= 80 else 'warning' if health_score >= 60 else 'critical',
            'issues': issues,
            'api_stats': api_stats,
            'db_stats': db_stats
        }

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_api_performance(f):
    """API性能监控装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        endpoint = f.__name__
        status_code = 200
        
        try:
            result = f(*args, **kwargs)
            # 尝试从Flask响应中获取状态码
            if hasattr(result, 'status_code'):
                status_code = result.status_code
            elif isinstance(result, tuple) and len(result) > 1:
                status_code = result[1]
            return result
        except Exception as e:
            status_code = 500
            raise
        finally:
            duration = (time.time() - start_time) * 1000  # 转换为毫秒
            performance_monitor.record_api_call(endpoint, duration, status_code)
            
            # 记录慢查询
            if duration > 2000:
                logger.warning(f"慢API调用: {endpoint} 耗时 {duration:.0f}ms")
    
    return decorated_function

def monitor_db_performance(query_type):
    """数据库性能监控装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()
            rows_affected = 0
            
            try:
                result = f(*args, **kwargs)
                # 尝试获取影响的行数
                if isinstance(result, list):
                    rows_affected = len(result)
                elif isinstance(result, dict) and 'affected_rows' in result:
                    rows_affected = result['affected_rows']
                return result
            finally:
                duration = (time.time() - start_time) * 1000  # 转换为毫秒
                performance_monitor.record_db_query(query_type, duration, rows_affected)
                
                # 记录慢查询
                if duration > 1000:
                    logger.warning(f"慢数据库查询: {query_type} 耗时 {duration:.0f}ms")
        
        return decorated_function
    return decorator

def get_performance_report():
    """获取性能报告"""
    return performance_monitor.get_health_status()
