from flask import jsonify
from datetime import datetime
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def api_response(data=None, message="success", status=200):
    """标准化API响应格式"""
    return jsonify({
        "success": status < 400,
        "data": data,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }), status

def handle_api_errors(f):
    """API错误处理装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"参数错误: {e}")
            return api_response(message=str(e), status=400)
        except Exception as e:
            logger.error(f"API错误: {e}")
            return api_response(message="服务器内部错误", status=500)
    return decorated_function

def validate_required_fields(data, required_fields):
    """验证必需字段"""
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        raise ValueError(f"缺少必需字段: {', '.join(missing_fields)}")
