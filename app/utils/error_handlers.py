"""
错误处理模块
统一的错误处理和响应
"""

import logging
from flask import jsonify, render_template, request

logger = logging.getLogger(__name__)

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        """400 错误处理"""
        logger.warning(f"400错误: {request.url} - {error}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '请求参数错误',
                'code': 400
            }), 400
        
        return render_template('error.html', 
                             error_code=400,
                             error_message='请求参数错误'), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        """401 错误处理"""
        logger.warning(f"401错误: {request.url} - {error}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '未授权访问',
                'code': 401
            }), 401
        
        from flask import redirect, url_for
        return redirect(url_for('auth.login'))
    
    @app.errorhandler(403)
    def forbidden(error):
        """403 错误处理"""
        logger.warning(f"403错误: {request.url} - {error}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '禁止访问',
                'code': 403
            }), 403
        
        return render_template('error.html',
                             error_code=403,
                             error_message='禁止访问'), 403
    
    @app.errorhandler(404)
    def not_found(error):
        """404 错误处理"""
        logger.info(f"404错误: {request.url}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '资源未找到',
                'code': 404
            }), 404
        
        return render_template('404.html'), 404
    
    @app.errorhandler(405)
    def method_not_allowed(error):
        """405 错误处理"""
        logger.warning(f"405错误: {request.method} {request.url}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '请求方法不允许',
                'code': 405
            }), 405
        
        return render_template('error.html',
                             error_code=405,
                             error_message='请求方法不允许'), 405
    
    @app.errorhandler(429)
    def rate_limit_exceeded(error):
        """429 错误处理 - 请求过于频繁"""
        logger.warning(f"429错误: {request.url} - 请求过于频繁")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '请求过于频繁，请稍后再试',
                'code': 429
            }), 429
        
        return render_template('error.html',
                             error_code=429,
                             error_message='请求过于频繁，请稍后再试'), 429
    
    @app.errorhandler(500)
    def internal_error(error):
        """500 错误处理"""
        logger.error(f"500错误: {request.url} - {error}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '服务器内部错误',
                'code': 500
            }), 500
        
        return render_template('error.html',
                             error_code=500,
                             error_message='服务器内部错误'), 500
    
    @app.errorhandler(502)
    def bad_gateway(error):
        """502 错误处理"""
        logger.error(f"502错误: {request.url} - {error}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '网关错误',
                'code': 502
            }), 502
        
        return render_template('error.html',
                             error_code=502,
                             error_message='网关错误'), 502
    
    @app.errorhandler(503)
    def service_unavailable(error):
        """503 错误处理"""
        logger.error(f"503错误: {request.url} - {error}")
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '服务暂时不可用',
                'code': 503
            }), 503
        
        return render_template('error.html',
                             error_code=503,
                             error_message='服务暂时不可用'), 503
    
    @app.errorhandler(Exception)
    def handle_exception(error):
        """通用异常处理"""
        logger.error(f"未处理的异常: {request.url} - {error}", exc_info=True)
        
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': '服务器内部错误',
                'code': 500
            }), 500
        
        return render_template('error.html',
                             error_code=500,
                             error_message='服务器内部错误'), 500

def create_api_response(data=None, message="success", success=True, status_code=200):
    """创建标准化的API响应"""
    response = {
        'success': success,
        'message': message,
        'timestamp': time.time()
    }
    
    if data is not None:
        response['data'] = data
    
    return jsonify(response), status_code

def create_error_response(message, code=400, details=None):
    """创建错误响应"""
    response = {
        'success': False,
        'error': message,
        'code': code,
        'timestamp': time.time()
    }
    
    if details:
        response['details'] = details
    
    return jsonify(response), code
