
<!DOCTYPE html>
<html>
<head>
    <title>承运商显示测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .carrier-name { color: #2563eb; font-weight: bold; }
    </style>
</head>
<body>
    <h1>承运商显示测试</h1>
    <div id="test-results"></div>
    
    <script>
        // 承运商映射函数（与前端一致）
        function getCarrierName(carrierCode) {
            const carriers = {
                '1': 'UPS', '2': 'USPS', '3': 'FedEx', '4': 'DHL', '5': 'TNT',
                '11': '中国邮政', '12': '中国EMS', '13': '顺丰速运',
                '21': 'Australia Post', '31': 'Deutsche Post', '41': 'Amazon Logistics',
                '99': '其他快递'
            };
            
            if (carrierCode && carriers[carrierCode.toString()]) {
                return carriers[carrierCode.toString()];
            }
            return '承运商';
        }
        
        // 测试数据
        const testData = [
            { carrier_code: '1', tracking_number: '1Z999AA1234567890' },
            { carrier_code: '3', tracking_number: '123456789012' },
            { carrier_code: '4', tracking_number: '1234567890' },
            { carrier_code: '11', tracking_number: 'CP123456789CN' },
            { carrier_code: '13', tracking_number: 'SF1234567890' },
            { carrier_code: null, tracking_number: '查询不到跟踪单号' },
            { carrier_code: '', tracking_number: 'UNKNOWN123456' }
        ];
        
        // 渲染测试结果
        const container = document.getElementById('test-results');
        testData.forEach((item, index) => {
            const div = document.createElement('div');
            div.className = 'test-item';
            div.innerHTML = `
                <strong>测试 ${index + 1}:</strong><br>
                承运商代码: ${item.carrier_code || '无'}<br>
                显示效果: <span class="carrier-name">${getCarrierName(item.carrier_code)}</span>: ${item.tracking_number}
            `;
            container.appendChild(div);
        });
    </script>
</body>
</html>
    