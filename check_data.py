#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_config import execute_query

def check_database_data():
    print('=== 检查数据库数据 ===')
    
    try:
        # 检查inventory表
        inventory_count = execute_query('SELECT COUNT(*) as count FROM inventory')
        print(f'Inventory表总记录数: {inventory_count[0]["count"]}')
        
        if inventory_count[0]['count'] > 0:
            # 检查有库存的记录
            inventory_with_stock = execute_query('SELECT COUNT(*) as count FROM inventory WHERE quantity > 0')
            print(f'有库存的记录数: {inventory_with_stock[0]["count"]}')
            
            # 查看库存样本
            inventory_sample = execute_query('SELECT sku, quantity, date FROM inventory WHERE quantity > 0 LIMIT 5')
            print('\n有库存的SKU样本:')
            for item in inventory_sample:
                print(f'  SKU: {item["sku"]}, 数量: {item["quantity"]}, 日期: {item["date"]}')
        
        # 检查orders表
        orders_count = execute_query('SELECT COUNT(*) as count FROM orders')
        print(f'\nOrders表总记录数: {orders_count[0]["count"]}')
        
        if orders_count[0]['count'] > 0:
            # 检查最近30天的订单
            recent_orders = execute_query('''
                SELECT COUNT(*) as count 
                FROM orders 
                WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ''')
            print(f'最近30天订单数: {recent_orders[0]["count"]}')
            
            # 查看订单样本
            orders_sample = execute_query('SELECT sku, product_quantity, order_date FROM orders LIMIT 5')
            print('\n订单样本:')
            for order in orders_sample:
                print(f'  SKU: {order["sku"]}, 数量: {order["product_quantity"]}, 日期: {order["order_date"]}')
        
        # 检查products表
        products_count = execute_query('SELECT COUNT(*) as count FROM products')
        print(f'\nProducts表总记录数: {products_count[0]["count"]}')
        
        print('\n=== 测试提醒查询 ===')
        # 简化的查询，不限制天数
        simple_query = '''
        SELECT
            i.sku,
            i.quantity as current_stock,
            COALESCE(s.total_sales_30d, 0) as total_sales_30d,
            CASE
                WHEN COALESCE(s.total_sales_30d, 0) > 0 THEN
                    ROUND((COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) * 30 / s.total_sales_30d, 1)
                ELSE 999999
            END as estimated_days
        FROM inventory i
        LEFT JOIN (
            SELECT sku, SUM(product_quantity) as total_sales_30d
            FROM orders
            WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY sku
        ) s ON i.sku = s.sku
        WHERE i.quantity > 0
        AND i.date = (SELECT MAX(i2.date) FROM inventory i2 WHERE i2.sku = i.sku)
        ORDER BY estimated_days ASC
        LIMIT 10
        '''
        
        alerts = execute_query(simple_query)
        print(f'简化查询结果数量: {len(alerts)}')
        
        if alerts:
            print('查询结果:')
            for alert in alerts:
                print(f'  SKU: {alert["sku"]}, 库存: {alert["current_stock"]}, 30天销量: {alert["total_sales_30d"]}, 预计天数: {alert["estimated_days"]}')
        else:
            print('没有找到任何结果')
            
            # 检查是否有最新日期的库存数据
            latest_date_query = '''
            SELECT 
                i.sku, 
                i.quantity, 
                i.date,
                (SELECT MAX(i2.date) FROM inventory i2 WHERE i2.sku = i.sku) as max_date
            FROM inventory i 
            WHERE i.quantity > 0 
            LIMIT 5
            '''
            
            latest_data = execute_query(latest_date_query)
            print(f'\n库存数据检查（前5条）:')
            for item in latest_data:
                print(f'  SKU: {item["sku"]}, 数量: {item["quantity"]}, 当前日期: {item["date"]}, 最新日期: {item["max_date"]}')
    
    except Exception as e:
        print(f'查询出错: {e}')

if __name__ == '__main__':
    check_database_data()
