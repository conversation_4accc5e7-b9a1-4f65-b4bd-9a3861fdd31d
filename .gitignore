# Python缓存
__pycache__/
*.py[cod]
*$py.class
*.so

# 虚拟环境
venv/
env/
ENV/

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.bak
*.old

# 测试文件
test_*.py
*_test.py
debug_*.py
*_debug.py
fix_*.py
*_fix.py

# 构建输出
build/
dist/

# 确保数据文件被包含（不要忽略）
# !data/
# !data/*.xlsx
# 环境配置
.env
.env.local
.env.production

# 日志文件
logs/
*.log

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
