#!/usr/bin/env python3
"""
KKUGUAN 项目快速优化脚本
实施高优先级的优化措施
"""

import os
import sys
import shutil
from pathlib import Path

def create_env_template():
    """创建环境变量模板文件"""
    env_template = """# KKUGUAN 环境配置
# 数据库配置
DB_HOST_PRIMARY=127.0.0.1
DB_HOST_FALLBACK=*************
DB_USER=Seller
DB_PASSWORD=your_secure_password_here
DB_NAME=kkuguan_db
DB_PORT=3306

# 应用配置
SECRET_KEY=your_random_secret_key_here
FLASK_ENV=development
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# 飞书API配置
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret
FEISHU_SHEET_TOKEN=your_sheet_token

# 17track API配置
TRACK_API_KEY=your_17track_api_key
"""
    
    with open('.env.template', 'w', encoding='utf-8') as f:
        f.write(env_template)
    
    print("✅ 创建了 .env.template 文件")
    print("📝 请复制为 .env 并填入真实配置")

def create_directory_structure():
    """创建优化后的目录结构"""
    directories = [
        'app',
        'app/models',
        'app/services', 
        'app/api',
        'app/web',
        'app/utils',
        'config',
        'logs',
        'tests',
        'requirements'
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        # 创建 __init__.py 文件
        if directory.startswith('app/'):
            init_file = Path(directory) / '__init__.py'
            if not init_file.exists():
                init_file.touch()
    
    print("✅ 创建了优化后的目录结构")

def create_config_files():
    """创建配置文件"""
    
    # 基础配置
    base_config = """import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key')
    
    # 数据库配置
    DB_HOST_PRIMARY = os.getenv('DB_HOST_PRIMARY', '127.0.0.1')
    DB_HOST_FALLBACK = os.getenv('DB_HOST_FALLBACK', '*************')
    DB_USER = os.getenv('DB_USER', 'Seller')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_NAME = os.getenv('DB_NAME', 'kkuguan_db')
    DB_PORT = int(os.getenv('DB_PORT', '3306'))
    
    # Redis配置
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')

class DevelopmentConfig(Config):
    DEBUG = True
    
class ProductionConfig(Config):
    DEBUG = False
    
class TestingConfig(Config):
    TESTING = True
    
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
"""
    
    with open('config/__init__.py', 'w', encoding='utf-8') as f:
        f.write(base_config)
    
    print("✅ 创建了配置文件")

def create_requirements_files():
    """创建分环境的依赖文件"""
    
    # 基础依赖
    base_requirements = """Flask==2.3.3
pandas==2.0.3
numpy==1.24.3
openpyxl==3.1.2
python-dateutil>=2.8.0
werkzeug>=2.3.0,<3.0.0
requests>=2.28.0,<3.0.0
pytz>=2023.3
redis>=4.0.0,<5.0.0
flask-caching>=2.0.0,<3.0.0
mysql-connector-python==8.1.0
schedule==1.2.0
flask-compress>=1.13
python-dotenv>=1.0.0
flask-wtf>=1.1.0
flask-limiter>=3.0.0
"""
    
    # 开发环境依赖
    dev_requirements = """-r base.txt
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
"""
    
    # 生产环境依赖
    prod_requirements = """-r base.txt
gunicorn>=20.1.0,<21.0.0
"""
    
    with open('requirements/base.txt', 'w') as f:
        f.write(base_requirements)
    
    with open('requirements/development.txt', 'w') as f:
        f.write(dev_requirements)
        
    with open('requirements/production.txt', 'w') as f:
        f.write(prod_requirements)
    
    print("✅ 创建了分环境依赖文件")

def create_api_utils():
    """创建API工具函数"""
    
    api_utils = """from flask import jsonify
from datetime import datetime
from functools import wraps
import logging

logger = logging.getLogger(__name__)

def api_response(data=None, message="success", status=200):
    \"\"\"标准化API响应格式\"\"\"
    return jsonify({
        "success": status < 400,
        "data": data,
        "message": message,
        "timestamp": datetime.utcnow().isoformat()
    }), status

def handle_api_errors(f):
    \"\"\"API错误处理装饰器\"\"\"
    @wraps(f)
    def decorated_function(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except ValueError as e:
            logger.warning(f"参数错误: {e}")
            return api_response(message=str(e), status=400)
        except Exception as e:
            logger.error(f"API错误: {e}")
            return api_response(message="服务器内部错误", status=500)
    return decorated_function

def validate_required_fields(data, required_fields):
    \"\"\"验证必需字段\"\"\"
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        raise ValueError(f"缺少必需字段: {', '.join(missing_fields)}")
"""
    
    with open('app/utils/api_helpers.py', 'w', encoding='utf-8') as f:
        f.write(api_utils)
    
    print("✅ 创建了API工具函数")

def create_basic_tests():
    """创建基础测试文件"""
    
    test_config = """import pytest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

@pytest.fixture
def app():
    from app import create_app
    app = create_app('testing')
    return app

@pytest.fixture
def client(app):
    return app.test_client()
"""
    
    test_api = """def test_health_check(client):
    \"\"\"测试健康检查端点\"\"\"
    response = client.get('/health')
    assert response.status_code == 200
    data = response.get_json()
    assert 'status' in data

def test_api_response_format(client):
    \"\"\"测试API响应格式\"\"\"
    response = client.get('/api/stats')
    assert response.status_code in [200, 401]  # 可能需要登录
"""
    
    with open('tests/conftest.py', 'w', encoding='utf-8') as f:
        f.write(test_config)
        
    with open('tests/test_api.py', 'w', encoding='utf-8') as f:
        f.write(test_api)
    
    print("✅ 创建了基础测试文件")

def update_gitignore():
    """更新.gitignore文件"""
    
    additional_ignores = """
# 环境配置
.env
.env.local
.env.production

# 日志文件
logs/
*.log

# 测试覆盖率
.coverage
htmlcov/
.pytest_cache/

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
"""
    
    with open('.gitignore', 'a', encoding='utf-8') as f:
        f.write(additional_ignores)
    
    print("✅ 更新了.gitignore文件")

def main():
    """主函数"""
    print("🚀 开始KKUGUAN项目快速优化...")
    
    try:
        create_env_template()
        create_directory_structure()
        create_config_files()
        create_requirements_files()
        create_api_utils()
        create_basic_tests()
        update_gitignore()
        
        print("\n🎉 快速优化完成！")
        print("\n📋 下一步操作：")
        print("1. 复制 .env.template 为 .env 并填入真实配置")
        print("2. 安装开发依赖: pip install -r requirements/development.txt")
        print("3. 运行测试: pytest tests/")
        print("4. 查看完整优化建议: cat OPTIMIZATION_RECOMMENDATIONS.md")
        
    except Exception as e:
        print(f"❌ 优化过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
