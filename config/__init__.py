import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    """基础配置类"""
    SECRET_KEY = os.getenv('SECRET_KEY', 'kkuguan-secret-key-2024')

    # 数据库配置
    DB_HOST_PRIMARY = os.getenv('DB_HOST_PRIMARY', '127.0.0.1')
    DB_HOST_FALLBACK = os.getenv('DB_HOST_FALLBACK', '*************')
    DB_USER = os.getenv('DB_USER', 'Seller')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '98c06z27W@')
    DB_NAME = os.getenv('DB_NAME', 'kkuguan_db')
    DB_PORT = int(os.getenv('DB_PORT', '3306'))

    # Redis配置
    REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
    REDIS_PORT = int(os.getenv('REDIS_PORT', '6379'))
    REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', '')

    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')

    # Flask压缩配置
    COMPRESS_MIMETYPES = [
        'text/html', 'text/css', 'text/xml', 'application/json',
        'application/javascript', 'text/javascript'
    ]
    COMPRESS_LEVEL = 6
    COMPRESS_MIN_SIZE = 500

    # 17track API配置
    TRACK_API_KEY = os.getenv('TRACK_API_KEY') or os.getenv('API_KEY')
    TRACK_REGISTER_URL = 'https://api.17track.net/track/v2.2/register'
    TRACK_GET_INFO_URL = 'https://api.17track.net/track/v2.2/gettrackinfo'

    # 登录配置
    LOGIN_USERS = {
        'admin': 'admin123',
        'kkuguan': '123456',
    }
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 300  # 5分钟

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    DEBUG = True

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
