#!/usr/bin/env python3
"""
KKUGUAN 库存管理系统 - 主入口文件
模块化架构版本
"""

import os
import sys
import logging
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    print("🚀 KKUGUAN 库存管理系统 - 模块化架构版本")
    print("=" * 50)

    # 设置环境
    env = os.getenv('FLASK_ENV', 'development')

    # 创建应用
    from app import create_app
    app = create_app(env)

    # 获取配置
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '5003'))
    debug = env == 'development'

    logger = logging.getLogger(__name__)
    logger.info(f"🚀 启动KKUGUAN系统 - {env}环境")
    logger.info(f"🌐 服务地址: http://{host}:{port}")
    logger.info(f"📁 架构: 模块化蓝图架构")
    logger.info(f"🔧 调试模式: {'开启' if debug else '关闭'}")

    try:
        # 启动应用
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        logger.info("👋 应用已停止")
    except Exception as e:
        logger.error(f"❌ 应用启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
