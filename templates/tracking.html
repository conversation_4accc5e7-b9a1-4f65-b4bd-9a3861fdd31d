{% extends "base.html" %}

{% block title %}KKUGUAN - 物流追踪{% endblock %}

{% block head %}
<style>
    /* 实时更新高亮效果 */
    .updated-highlight {
        background-color: #fef3c7 !important;
        border: 2px solid #f59e0b !important;
        animation: pulse-highlight 2s ease-in-out;
    }

    @keyframes pulse-highlight {
        0% { background-color: #fef3c7; }
        50% { background-color: #fde68a; }
        100% { background-color: #fef3c7; }
    }

    /* 通知样式 */
    .notification {
        animation: slideInRight 0.3s ease-out;
    }

    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    /* 实时更新状态指示器 */
    .realtime-indicator {
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1000;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .realtime-indicator.connected {
        background-color: #10b981;
        color: white;
    }

    .realtime-indicator.disconnected {
        background-color: #ef4444;
        color: white;
    }

    .realtime-indicator .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: currentColor;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <!-- 实时更新状态指示器 -->
    <div id="realtime-indicator" class="realtime-indicator disconnected" style="display: none;">
        <div class="status-dot"></div>
        <span id="realtime-status">连接中...</span>
    </div>

    <!-- 页面标题 -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">物流追踪管理</h1>
        <p class="text-gray-600">实时追踪物流状态，管理邀评和备注信息</p>
    </div>

    <!-- 仪表板统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300 cursor-pointer dashboard-card" 
             data-filter-status="Delivered">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">已送达</p>
                    <p class="text-3xl font-bold text-green-600" id="delivered-count">-</p>
                </div>
                <div class="bg-gradient-to-br from-green-400 to-green-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300 cursor-pointer dashboard-card" 
             data-filter-status="InTransit">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">运输中</p>
                    <p class="text-3xl font-bold text-blue-600" id="in-transit-count">-</p>
                </div>
                <div class="bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300 cursor-pointer dashboard-card" 
             data-filter-long-transit="true">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">运输过久</p>
                    <p class="text-3xl font-bold text-orange-600" id="long-transit-count">-</p>
                </div>
                <div class="bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300 cursor-pointer dashboard-card" 
             data-filter-exception="true">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">异常</p>
                    <p class="text-3xl font-bold text-red-600" id="exception-count">-</p>
                </div>
                <div class="bg-gradient-to-br from-red-400 to-red-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <div class="bg-white/80 shadow-xl rounded-3xl p-6 hover:shadow-2xl transition-all duration-300 cursor-pointer dashboard-card" 
             data-filter-uninvited-review="false">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-500 mb-2">可邀评</p>
                    <p class="text-3xl font-bold text-purple-600" id="invitable-count">-</p>
                </div>
                <div class="bg-gradient-to-br from-purple-400 to-purple-600 rounded-2xl p-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具栏 -->
    <div class="bg-white/80 shadow-xl rounded-3xl p-6 mb-8">
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="flex items-center gap-4">
                <h2 class="text-xl font-semibold text-gray-900">物流列表</h2>
                <div class="flex items-center gap-2">
                    <span class="text-sm text-gray-500">订单数:</span>
                    <span class="text-sm font-semibold text-blue-600" id="total-count">0</span>
                    <span class="text-xs text-gray-400" id="package-count"></span>
                </div>
            </div>
            
            <div class="flex items-center gap-4">
                <!-- 筛选器 -->
                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">状态:</label>
                    <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部</option>
                        <option value="Delivered">已送达</option>
                        <option value="InTransit">运输中</option>
                        <option value="Exception">异常</option>
                        <option value="Pending">待处理</option>
                    </select>
                </div>

                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">平台:</label>
                    <select id="platform-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部</option>
                    </select>
                </div>

                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">店铺:</label>
                    <select id="shop-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部</option>
                    </select>
                </div>

                <div class="flex items-center gap-2">
                    <label class="text-sm font-medium text-gray-700">邀评:</label>
                    <select id="review-filter" class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">全部</option>
                        <option value="false">未邀评</option>
                        <option value="true">已邀评</option>
                    </select>
                </div>

                <!-- 同步新物流单号到17track按钮 -->
                <button onclick="syncTrackingFromDB()"
                        title="只同步没有跟踪记录的新物流单号，已有记录的单号通过webhook自动更新"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    同步新单号
                </button>

                <!-- 重试失败物流单号按钮 -->
                <button onclick="retryFailedTracking()"
                        class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 2v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15M12 9l3 3-3 3"></path>
                    </svg>
                    重试失败
                </button>

                <!-- 上传Excel按钮 -->
                <div class="relative">
                    <input type="file" id="excel-upload" accept=".xlsx,.xls" class="hidden">
                    <button onclick="document.getElementById('excel-upload').click()"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        上传Excel
                    </button>
                </div>

                <!-- 刷新按钮 -->
                <button onclick="fetchTrackingDataAndRender()"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    刷新
                </button>
            </div>
        </div>
    </div>

    <!-- 物流列表 -->
    <div class="bg-white/80 shadow-xl rounded-3xl p-6">
        <div id="tracking-list" class="space-y-4">
            <!-- 物流项目将在这里动态加载 -->
            <div class="text-center py-8">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-500">正在加载物流数据...</p>
            </div>
        </div>

        <!-- 分页 -->
        <div class="flex items-center justify-between mt-6 pt-6 border-t border-gray-200">
            <div class="text-sm text-gray-500">
                显示第 <span id="page-start">0</span> - <span id="page-end">0</span> 个订单，共 <span id="page-total">0</span> 个订单
            </div>
            <div class="flex items-center gap-2">
                <button id="prev-page" class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    上一页
                </button>
                <span class="px-3 py-2 text-sm">
                    第 <span id="current-page">1</span> / <span id="total-pages">1</span> 页
                </span>
                <button id="next-page" class="px-3 py-2 border border-gray-300 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    下一页
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑备注模态框 -->
<div id="edit-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-3xl p-6 w-full max-w-md">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">编辑物流信息</h3>

            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">订单号</label>
                    <input type="text" id="edit-order-number" readonly
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">跟踪单号</label>
                    <input type="text" id="edit-tracking-number"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="输入跟踪单号...">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                    <textarea id="edit-remark" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="请输入备注信息..."></textarea>
                </div>

                <div class="flex items-center">
                    <input type="checkbox" id="edit-uninvited-review"
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="edit-uninvited-review" class="ml-2 text-sm text-gray-700">已邀评</label>
                </div>
            </div>
            
            <div class="flex items-center justify-end gap-3 mt-6">
                <button onclick="closeEditModal()" 
                        class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50">
                    取消
                </button>
                <button onclick="saveTrackingData()" 
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium">
                    保存
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 全局变量
let allTrackingData = {};
let filteredData = {};
let currentPage = 1;
const itemsPerPage = 20;

// 实时更新功能
let eventSource = null;

function initializeRealTimeUpdates() {
    try {
        updateRealtimeIndicator('connecting', '连接中...');

        eventSource = new EventSource('/api/tracking/events');

        eventSource.onopen = function(event) {
            console.log('✅ 实时更新连接已建立');
            updateRealtimeIndicator('connected', '实时更新已连接');
            showNotification('实时更新已连接', 'success');
        };

        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                handleRealTimeUpdate(data);
            } catch (e) {
                console.error('解析实时更新数据失败:', e);
            }
        };

        eventSource.onerror = function(event) {
            console.error('实时更新连接错误:', event);
            updateRealtimeIndicator('disconnected', '连接中断，重连中...');
            showNotification('实时更新连接中断，将尝试重连', 'warning');

            // 5秒后重连
            setTimeout(() => {
                if (eventSource.readyState === EventSource.CLOSED) {
                    initializeRealTimeUpdates();
                }
            }, 5000);
        };

    } catch (e) {
        console.error('初始化实时更新失败:', e);
        updateRealtimeIndicator('disconnected', '连接失败');
    }
}

function updateRealtimeIndicator(status, message) {
    const indicator = document.getElementById('realtime-indicator');
    const statusText = document.getElementById('realtime-status');

    if (indicator && statusText) {
        indicator.style.display = 'flex';
        indicator.className = `realtime-indicator ${status}`;
        statusText.textContent = message;
    }
}

function handleRealTimeUpdate(data) {
    switch (data.type) {
        case 'connected':
            console.log('实时更新服务已连接');
            break;

        case 'tracking_update':
            console.log('收到物流更新:', data);
            updateTrackingItem(data);
            showNotification(`物流单号 ${data.tracking_number} 状态已更新`, 'info');
            break;

        case 'heartbeat':
            console.log('收到心跳信号');
            break;

        default:
            console.log('未知的实时更新类型:', data.type);
    }
}

function updateTrackingItem(updateData) {
    const trackingNumber = updateData.tracking_number;
    const trackingCard = document.querySelector(`[data-tracking="${trackingNumber}"]`);

    if (trackingCard) {
        // 更新状态
        const statusElement = trackingCard.querySelector('.tracking-status');
        if (statusElement && updateData.status) {
            statusElement.textContent = updateData.status;
            statusElement.classList.add('updated-highlight');
            setTimeout(() => {
                statusElement.classList.remove('updated-highlight');
            }, 2000);
        }

        // 更新最新事件
        if (updateData.latest_event) {
            const eventElement = trackingCard.querySelector('.latest-event');
            if (eventElement) {
                eventElement.innerHTML = `
                    <div class="font-medium">${updateData.latest_event.description}</div>
                    <div class="text-sm text-gray-500">
                        ${updateData.latest_event.location} • ${formatDateTime(updateData.latest_event.time_iso)}
                    </div>
                `;
                eventElement.classList.add('updated-highlight');
                setTimeout(() => {
                    eventElement.classList.remove('updated-highlight');
                }, 2000);
            }
        }

        // 重新获取完整数据以更新详细轨迹
        fetchTrackingDataAndRender();
    }
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 ${getNotificationClass(type)}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // 3秒后自动消失
    setTimeout(() => {
        notification.style.opacity = '0';
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

function getNotificationClass(type) {
    switch (type) {
        case 'success': return 'bg-green-500 text-white';
        case 'warning': return 'bg-yellow-500 text-white';
        case 'error': return 'bg-red-500 text-white';
        case 'info':
        default: return 'bg-blue-500 text-white';
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    fetchTrackingDataAndRender();
    fetchDashboardSummary();
    setupEventListeners();
    initializeRealTimeUpdates();

    // 减少自动刷新频率（因为有实时更新）
    setInterval(fetchDashboardSummary, 60000); // 改为60秒刷新统计数据
});

// 页面卸载时关闭SSE连接
window.addEventListener('beforeunload', function() {
    if (eventSource) {
        eventSource.close();
    }
});

// 设置事件监听器
function setupEventListeners() {
    // 筛选器事件
    document.getElementById('status-filter').addEventListener('change', applyFilters);
    document.getElementById('platform-filter').addEventListener('change', applyFilters);
    document.getElementById('review-filter').addEventListener('change', applyFilters);

    // 店铺筛选器特殊处理：选择店铺时自动选择对应平台
    document.getElementById('shop-filter').addEventListener('change', function() {
        const selectedShop = this.value;
        if (selectedShop && window.shopToPlatformMap) {
            const correspondingPlatform = window.shopToPlatformMap.get(selectedShop);
            if (correspondingPlatform) {
                document.getElementById('platform-filter').value = correspondingPlatform;
            }
        }
        applyFilters();
    });
    
    // 分页事件
    document.getElementById('prev-page').addEventListener('click', () => changePage(-1));
    document.getElementById('next-page').addEventListener('click', () => changePage(1));
    
    // Excel上传事件
    document.getElementById('excel-upload').addEventListener('change', handleExcelUpload);
    
    // 仪表板卡片点击事件
    document.querySelectorAll('.dashboard-card').forEach(card => {
        card.addEventListener('click', function() {
            const filterStatus = this.dataset.filterStatus;
            const filterLongTransit = this.dataset.filterLongTransit;
            const filterException = this.dataset.filterException;
            const filterUninvitedReview = this.dataset.filterUninvitedReview;
            
            // 重置所有筛选器
            document.getElementById('status-filter').value = '';
            document.getElementById('platform-filter').value = '';
            document.getElementById('shop-filter').value = '';
            document.getElementById('review-filter').value = '';
            
            // 应用特定筛选
            if (filterStatus) {
                document.getElementById('status-filter').value = filterStatus;
            }
            if (filterUninvitedReview !== undefined) {
                document.getElementById('review-filter').value = filterUninvitedReview;
            }
            
            currentPage = 1;
            applyFilters();
        });
    });
}

// 获取物流追踪数据
async function fetchTrackingDataAndRender() {
    try {
        // 保存当前筛选状态
        const currentFilters = {
            status: document.getElementById('status-filter').value,
            platform: document.getElementById('platform-filter').value,
            shop: document.getElementById('shop-filter').value,
            review: document.getElementById('review-filter').value
        };

        const response = await fetch('/api/tracking');
        allTrackingData = await response.json();

        populateFilters();

        // 恢复筛选状态
        document.getElementById('status-filter').value = currentFilters.status;
        document.getElementById('platform-filter').value = currentFilters.platform;
        document.getElementById('shop-filter').value = currentFilters.shop;
        document.getElementById('review-filter').value = currentFilters.review;

        applyFilters();

    } catch (error) {
        console.error('获取物流数据失败:', error);
        document.getElementById('tracking-list').innerHTML = '<p class="text-center text-red-500">加载物流信息失败。</p>';
    }
}

// 获取仪表板统计数据
async function fetchDashboardSummary() {
    try {
        const response = await fetch('/api/dashboard_summary');
        const summary = await response.json();

        document.getElementById('delivered-count').textContent = summary.delivered_count || 0;
        document.getElementById('in-transit-count').textContent = summary.in_transit_count || 0;
        document.getElementById('long-transit-count').textContent = summary.long_transit_count || 0;
        document.getElementById('exception-count').textContent = summary.exception_count || 0;
        document.getElementById('invitable-count').textContent = summary.invitable_count || 0;

    } catch (error) {
        console.error('获取仪表板统计失败:', error);
    }
}

// 填充筛选器选项
function populateFilters() {
    const platforms = new Set();
    const shops = new Set();
    const shopToPlatform = new Map(); // 店铺到平台的映射

    Object.values(allTrackingData).forEach(item => {
        if (item.platform) platforms.add(item.platform);
        if (item.shop) {
            shops.add(item.shop);
            // 建立店铺到平台的映射关系
            if (item.platform) {
                shopToPlatform.set(item.shop, item.platform);
            }
        }
    });

    // 存储映射关系到全局变量
    window.shopToPlatformMap = shopToPlatform;

    // 填充店铺筛选器
    const shopFilter = document.getElementById('shop-filter');
    shopFilter.innerHTML = '<option value="">全部</option>';
    shops.forEach(shop => {
        const option = document.createElement('option');
        option.value = shop;
        option.textContent = shop;
        shopFilter.appendChild(option);
    });

    // 填充平台筛选器
    const platformFilter = document.getElementById('platform-filter');
    platformFilter.innerHTML = '<option value="">全部</option>';
    platforms.forEach(platform => {
        const option = document.createElement('option');
        option.value = platform;
        option.textContent = platform;
        platformFilter.appendChild(option);
    });
}

// 应用筛选器
function applyFilters() {
    const statusFilter = document.getElementById('status-filter').value;
    const platformFilter = document.getElementById('platform-filter').value;
    const shopFilter = document.getElementById('shop-filter').value;
    const reviewFilter = document.getElementById('review-filter').value;

    // 先按订单分组所有数据
    const groupedByOrder = {};
    Object.entries(allTrackingData).forEach(([trackingNumber, item]) => {
        const orderNumber = item.order_number || '无订单号';
        if (!groupedByOrder[orderNumber]) {
            groupedByOrder[orderNumber] = {
                orderInfo: item,
                trackingItems: []
            };
        }
        groupedByOrder[orderNumber].trackingItems.push({
            trackingNumber: trackingNumber,
            ...item
        });
    });

    // 筛选订单
    filteredData = {};
    Object.entries(groupedByOrder).forEach(([orderNumber, orderGroup]) => {
        let includeOrder = true;
        const orderInfo = orderGroup.orderInfo;
        const trackingItems = orderGroup.trackingItems;

        // 平台筛选（基于订单信息）
        if (platformFilter && orderInfo.platform !== platformFilter) {
            includeOrder = false;
        }

        // 店铺筛选（基于订单信息）
        if (shopFilter && orderInfo.shop !== shopFilter) {
            includeOrder = false;
        }

        // 状态筛选（订单下任何包裹匹配即可）
        if (statusFilter) {
            const hasMatchingStatus = trackingItems.some(item => item.status === statusFilter);
            if (!hasMatchingStatus) {
                includeOrder = false;
            }
        }

        // 邀评筛选（基于订单级别的邀评状态）
        if (reviewFilter !== '') {
            const isUninvited = reviewFilter === 'true';
            const hasUninvitedReview = trackingItems.some(item => item.is_uninvited_review);
            if (hasUninvitedReview !== isUninvited) {
                includeOrder = false;
            }
        }

        // 如果订单通过筛选，将其所有跟踪单号添加到filteredData
        if (includeOrder) {
            trackingItems.forEach(item => {
                filteredData[item.trackingNumber] = item;
            });
        }
    });

    currentPage = 1;
    renderTrackingList();
}

// 渲染物流列表
function renderTrackingList() {
    const trackingList = document.getElementById('tracking-list');

    // 按订单号分组数据
    const groupedByOrder = {};
    Object.entries(filteredData).forEach(([trackingNumber, item]) => {
        const orderNumber = item.order_number || '无订单号';
        if (!groupedByOrder[orderNumber]) {
            groupedByOrder[orderNumber] = {
                orderInfo: item, // 使用第一个跟踪单号的订单信息
                trackingItems: []
            };
        }
        groupedByOrder[orderNumber].trackingItems.push({
            trackingNumber: item.tracking_number || '', // 使用实际的跟踪单号，可能为空
            ...item
        });
    });

    // 转换为数组并按下单日期排序（最新的在前面）
    const ordersArray = Object.entries(groupedByOrder).map(([orderNumber, data]) => ({
        orderNumber,
        ...data
    }));

    ordersArray.sort((a, b) => {
        const dateA = a.orderInfo.order_date ? new Date(a.orderInfo.order_date) : new Date(0);
        const dateB = b.orderInfo.order_date ? new Date(b.orderInfo.order_date) : new Date(0);
        return dateB - dateA; // 降序排列，最新的在前面
    });

    const totalItems = ordersArray.length;
    const totalPackages = ordersArray.reduce((sum, order) => sum + order.trackingItems.length, 0);

    // 更新总计数（显示订单数量和包裹数量）
    document.getElementById('total-count').textContent = totalItems;
    document.getElementById('package-count').textContent = `(${totalPackages} 个包裹)`;

    // 计算分页
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    const pageData = ordersArray.slice(startIndex, endIndex);

    // 更新分页信息
    document.getElementById('page-start').textContent = totalItems > 0 ? startIndex + 1 : 0;
    document.getElementById('page-end').textContent = endIndex;
    document.getElementById('page-total').textContent = totalItems;
    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;

    // 更新分页按钮状态
    document.getElementById('prev-page').disabled = currentPage <= 1;
    document.getElementById('next-page').disabled = currentPage >= totalPages;

    if (pageData.length === 0) {
        trackingList.innerHTML = '<div class="text-center py-8"><p class="text-gray-500">没有找到匹配的物流信息</p></div>';
        return;
    }

    // 渲染按订单分组的物流项目
    trackingList.innerHTML = pageData.map((orderGroup) => {
        const orderInfo = orderGroup.orderInfo;
        const trackingItems = orderGroup.trackingItems;

        // 获取订单级别的状态信息
        const hasUninvitedReview = trackingItems.some(item => item.is_uninvited_review);
        const hasLongTransit = trackingItems.some(item => checkLongTransit(item));

        return `
            <div class="border border-gray-200 rounded-xl p-4 hover:shadow-lg transition-shadow duration-200">
                <!-- 订单头部信息 -->
                <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center gap-4 flex-1">
                        <span class="font-mono text-lg font-semibold text-gray-900">${getDisplayOrderNumber(orderInfo)}</span>
                        <span class="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">${trackingItems.length} 个包裹</span>
                        ${hasLongTransit ? '<span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">运输过久</span>' : ''}
                        ${hasUninvitedReview ? '<span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">已邀评</span>' : ''}
                    </div>

                    <!-- 右侧操作区域 -->
                    <div class="flex flex-col items-end gap-3 ml-4">
                        <!-- 编辑按钮 -->
                        <button onclick="openEditModal('${trackingItems[0].trackingNumber}')"
                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors duration-200">
                            编辑订单
                        </button>

                        <!-- 邀评勾选框 -->
                        <div class="flex items-center gap-2">
                            <input type="checkbox"
                                   id="review-${orderInfo.order_number}"
                                   ${hasUninvitedReview ? 'checked' : ''}
                                   onchange="toggleOrderReview('${orderInfo.order_number}', this.checked)"
                                   class="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 cursor-pointer">
                            <label for="review-${orderInfo.order_number}" class="text-sm font-medium text-gray-700 cursor-pointer">已邀评</label>
                        </div>
                    </div>
                </div>

                <!-- 订单基本信息 -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-4">
                    <div>
                        <span class="font-medium">平台:</span> ${orderInfo.platform || '-'}
                    </div>
                    <div>
                        <span class="font-medium">店铺:</span> ${orderInfo.shop || '-'}
                    </div>
                    <div>
                        <span class="font-medium">下单日期:</span> ${orderInfo.order_date ? new Date(orderInfo.order_date).toLocaleDateString('zh-CN') : '-'}
                    </div>
                    <div>
                        <span class="font-medium">包裹数量:</span> ${trackingItems.length} 个
                    </div>
                </div>

                <!-- 跟踪单号列表 -->
                <div class="space-y-3">
                    ${trackingItems.map((item, index) => {
                        const statusColor = getStatusColor(item.status);
                        const statusText = getStatusText(item.status);
                        const isLongTransit = checkLongTransit(item);
                        const trackingNumber = item.trackingNumber;

                        return `
                            <div class="border-l-4 ${statusColor.includes('green') ? 'border-green-400' : statusColor.includes('blue') ? 'border-blue-400' : statusColor.includes('yellow') ? 'border-yellow-400' : statusColor.includes('red') ? 'border-red-400' : 'border-gray-400'} pl-4 py-2 bg-gray-50 rounded-r-lg">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-2">
                                            <span class="font-mono text-sm font-semibold text-gray-900">
                                                <span class="text-blue-600">${getCarrierName(item.carrier_code)}</span>: ${trackingNumber || '查询不到跟踪单号'}
                                            </span>
                                            <span class="px-2 py-1 rounded-full text-xs font-medium ${statusColor}">${statusText}</span>
                                            ${isLongTransit ? '<span class="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">运输过久</span>' : ''}
                                        </div>

                                        <div class="text-sm text-gray-600 mb-2">
                                            <span class="font-medium">最后更新:</span> ${formatDateTime(item.updated_at)}
                                        </div>

                                        ${item.latestEvent ? `
                                            <div class="mt-2 p-2 bg-white rounded-lg border">
                                                <div class="text-sm">
                                                    <div class="flex items-center justify-between cursor-pointer" onclick="toggleTrackingHistory('${trackingNumber}')">
                                                        <div class="font-medium text-gray-900 mb-1">最新事件</div>
                                                        <svg class="w-4 h-4 text-gray-500 transform transition-transform duration-200" id="arrow-${trackingNumber}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="text-gray-600">${item.latestEvent.description || '-'}</div>
                                                    <div class="text-gray-500 text-xs mt-1">
                                                        ${item.latestEvent.location || ''} • ${formatDateTime(item.latestEvent.time_iso)}
                                                    </div>

                                                    <!-- 完整物流轨迹 (折叠) -->
                                                    <div id="history-${trackingNumber}" class="hidden mt-3 pt-3 border-t border-gray-200">
                                                        <div class="font-medium text-gray-900 mb-2">完整物流轨迹</div>
                                                        ${renderTrackingHistory(item)}
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}

                                        ${item.remark ? `
                                            <div class="mt-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                                                <div class="text-sm">
                                                    <div class="font-medium text-blue-900 mb-1">备注</div>
                                                    <div class="text-blue-700">${item.remark}</div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    }).join('');
}

// 获取状态颜色
function getStatusColor(status) {
    const colors = {
        'Delivered': 'bg-green-100 text-green-800',
        'InTransit': 'bg-blue-100 text-blue-800',
        'OutForDelivery': 'bg-yellow-100 text-yellow-800',
        'Exception': 'bg-red-100 text-red-800',
        'Pending': 'bg-gray-100 text-gray-800',
        'Unknown': 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
}

// 获取状态文本
function getStatusText(status) {
    const texts = {
        'Delivered': '已送达',
        'InTransit': '运输中',
        'OutForDelivery': '派送中',
        'Exception': '异常',
        'Pending': '待处理',
        'Unknown': '未知'
    };
    return texts[status] || status;
}

// 获取承运商名称
function getCarrierName(carrierCode) {
    const carriers = {
        // 17track实际承运商代码映射（基于数据库实际数据）
        '100003': 'USPS',
        '100002': 'UPS',
        '100297': 'FedEx',
        '100308': 'DHL',
        '100221': 'Canada Post',
        '101034': 'Royal Mail',
        '100929': 'Australia Post',

        // 17track标准承运商代码映射
        // 主要国际快递
        '1': 'UPS',
        '2': 'USPS',
        '3': 'FedEx',
        '4': 'DHL',
        '5': 'TNT',
        '6': 'DPD',
        '7': 'GLS',
        '8': 'Hermes',
        '9': 'Royal Mail',
        '10': 'Canada Post',

        // 中国邮政和快递
        '11': '中国邮政',
        '12': '中国EMS',
        '13': '顺丰速运',
        '14': '申通快递',
        '15': '圆通速递',
        '16': '韵达快递',
        '17': '中通快递',
        '18': '百世快递',
        '19': '德邦快递',
        '20': '京东物流',

        // 亚太地区
        '21': 'Australia Post',
        '22': 'Japan Post',
        '23': 'Singapore Post',
        '24': 'Hong Kong Post',
        '25': 'Malaysia Post',
        '26': 'Thailand Post',
        '27': 'Korea Post',
        '28': 'India Post',
        '29': 'New Zealand Post',
        '30': 'Philippines Post',

        // 欧洲快递
        '31': 'Deutsche Post',
        '32': 'PostNL',
        '33': 'Colissimo',
        '34': 'Chronopost',
        '35': 'Swiss Post',
        '36': 'Austrian Post',
        '37': 'Belgium Post',
        '38': 'Italy Post',
        '39': 'Spain Post',
        '40': 'Portugal Post',

        // 电商物流
        '41': 'Amazon Logistics',
        '42': 'OnTrac',
        '43': 'LaserShip',
        '44': 'Newgistics',
        '45': 'OSM',
        '46': 'Pitney Bowes',
        '47': 'Globegistics',
        '48': 'APC',
        '49': 'Asendia',
        '50': 'Yanwen',

        // 中东非洲
        '51': 'Aramex',
        '52': 'Emirates Post',
        '53': 'Saudi Post',
        '54': 'South Africa Post',
        '55': 'Egypt Post',

        // 南美洲
        '56': 'Brazil Post',
        '57': 'Argentina Post',
        '58': 'Chile Post',
        '59': 'Mexico Post',

        // 其他常见快递
        '60': 'SF Express',
        '61': 'YTO Express',
        '62': 'ZTO Express',
        '63': 'STO Express',
        '64': 'YUNDA Express',
        '65': 'Best Express',
        '66': 'J&T Express',
        '67': '4PX',
        '68': 'Cainiao',
        '69': 'AliExpress Shipping',
        '70': 'ePacket',

        // 特殊情况
        '99': '其他快递',
        '0': '自动检测'
    };

    // 如果有承运商代码，返回对应名称
    if (carrierCode && carriers[carrierCode.toString()]) {
        return carriers[carrierCode.toString()];
    }

    // 如果没有承运商代码，返回默认值
    return '承运商';
}

// 获取显示的订单号（eBay平台显示platform_id，其他平台显示order_number）
function getDisplayOrderNumber(item) {
    // 检查是否为eBay平台（不区分大小写）
    const platform = (item.platform || '').toLowerCase();
    const isEbayPlatform = platform.includes('ebay');

    if (isEbayPlatform && item.platform_id) {
        return item.platform_id;
    }

    return item.order_number || '无订单号';
}

// 检查是否运输过久
function checkLongTransit(item) {
    const status = item.status || 'Unknown';
    if (!['InTransit', 'OutForDelivery', 'InfoReceived'].includes(status)) {
        return false;
    }

    let days = 0;
    if (item.fullTrackInfo && item.fullTrackInfo.time_metrics) {
        const timeMetrics = item.fullTrackInfo.time_metrics;
        days = timeMetrics.days_of_transit || timeMetrics.days_after_last_update || 0;
    } else if (item.latestEvent && item.latestEvent.time_iso) {
        const eventDate = new Date(item.latestEvent.time_iso);
        const now = new Date();
        days = Math.floor((now - eventDate) / (1000 * 60 * 60 * 24));
    }

    return days > 7;
}

// 格式化日期时间
function formatDateTime(dateString) {
    if (!dateString) return '-';
    try {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch {
        return dateString;
    }
}

// 切换物流轨迹历史显示
function toggleTrackingHistory(trackingNumber) {
    const historyDiv = document.getElementById(`history-${trackingNumber}`);
    const arrow = document.getElementById(`arrow-${trackingNumber}`);

    if (historyDiv.classList.contains('hidden')) {
        historyDiv.classList.remove('hidden');
        arrow.style.transform = 'rotate(180deg)';
    } else {
        historyDiv.classList.add('hidden');
        arrow.style.transform = 'rotate(0deg)';
    }
}

// 渲染物流轨迹历史
function renderTrackingHistory(item) {
    // 检查数据结构
    if (!item.fullTrackInfo || typeof item.fullTrackInfo !== 'object') {
        return '<div class="text-gray-500 text-sm">暂无详细轨迹信息</div>';
    }

    // 检查tracking和providers
    const tracking = item.fullTrackInfo.tracking;
    if (!tracking || !tracking.providers || !Array.isArray(tracking.providers)) {
        return '<div class="text-gray-500 text-sm">暂无详细轨迹信息</div>';
    }

    const providers = tracking.providers;
    let historyHtml = '';

    providers.forEach(provider => {
        if (provider.events && Array.isArray(provider.events) && provider.events.length > 0) {
            const providerName = provider.provider?.name || '未知承运商';
            const providerCountry = provider.provider?.country || '';

            historyHtml += `
                <div class="mb-3">
                    <div class="flex items-center gap-2 mb-2">
                        <div class="text-sm font-medium text-gray-800">${providerName}</div>
                        ${providerCountry ? `<div class="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded">${providerCountry}</div>` : ''}
                    </div>
                    <div class="space-y-1">
                        ${provider.events.map((event, index) => `
                            <div class="flex gap-3 items-center py-1">
                                <div class="flex flex-col items-center">
                                    <div class="w-2.5 h-2.5 rounded-full ${index === 0 ? 'bg-blue-500' : 'bg-gray-300'}"></div>
                                    ${index < provider.events.length - 1 ? '<div class="w-0.5 h-4 bg-gray-200 mt-0.5"></div>' : ''}
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center gap-2 text-xs">
                                        <span class="font-medium text-gray-900 truncate">
                                            ${event.description || '无描述'}
                                        </span>
                                        <span class="text-gray-500 whitespace-nowrap">
                                            ${event.location || '位置未知'}
                                        </span>
                                        <span class="text-gray-400 whitespace-nowrap">
                                            ${formatDateTime(event.time_iso)}
                                        </span>
                                        ${event.sub_status ? `
                                            <span class="text-blue-600 whitespace-nowrap">
                                                ${getSubStatusText(event.sub_status)}
                                            </span>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    });

    if (!historyHtml) {
        return '<div class="text-gray-500 text-sm">暂无详细轨迹信息</div>';
    }

    return historyHtml;
}

// 获取子状态文本
function getSubStatusText(subStatus) {
    const subStatusTexts = {
        'InTransit_PickedUp': '已取件',
        'InTransit_Departure': '已发出',
        'InTransit_Arrival': '已到达',
        'InTransit_Other': '运输中',
        'OutForDelivery_Other': '派送中',
        'Delivered_Other': '已送达',
        'Exception_Other': '异常',
        'InfoReceived_Other': '信息已接收'
    };
    return subStatusTexts[subStatus] || subStatus;
}

// 分页
function changePage(direction) {
    // 计算订单数量而不是跟踪单号数量
    const groupedByOrder = {};
    Object.entries(filteredData).forEach(([trackingNumber, item]) => {
        const orderNumber = item.order_number || '无订单号';
        groupedByOrder[orderNumber] = true;
    });

    const totalOrders = Object.keys(groupedByOrder).length;
    const totalPages = Math.ceil(totalOrders / itemsPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        renderTrackingList();
    }
}

// 打开编辑模态框
function openEditModal(key) {
    const item = allTrackingData[key];
    if (!item) return;

    document.getElementById('edit-order-number').value = item.order_number || '无订单号';
    document.getElementById('edit-tracking-number').value = item.tracking_number || '';
    document.getElementById('edit-remark').value = item.remark || '';
    document.getElementById('edit-uninvited-review').checked = item.is_uninvited_review || false;

    // 存储当前编辑的key
    document.getElementById('edit-modal').setAttribute('data-key', key);
    document.getElementById('edit-modal').classList.remove('hidden');
}

// 关闭编辑模态框
function closeEditModal() {
    document.getElementById('edit-modal').classList.add('hidden');
}

// 保存物流数据
async function saveTrackingData() {
    const key = document.getElementById('edit-modal').getAttribute('data-key');
    const item = allTrackingData[key];
    if (!item) return;

    const originalTrackingNumber = item.tracking_number || '';
    const newTrackingNumber = document.getElementById('edit-tracking-number').value.trim();
    const remark = document.getElementById('edit-remark').value;
    const isUninvitedReview = document.getElementById('edit-uninvited-review').checked;
    const orderNumber = item.order_number;

    try {
        const response = await fetch('/api/update_local_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                orderNumber: orderNumber,
                originalTrackingNumber: originalTrackingNumber,
                newTrackingNumber: newTrackingNumber,
                remark: remark,
                isUninvitedReview: isUninvitedReview
            })
        });

        const result = await response.json();

        if (result.status === 'success') {
            // 刷新整个数据，因为跟踪单号可能已更改
            await fetchTrackingDataAndRender();

            // 刷新统计数据
            fetchDashboardSummary();

            closeEditModal();

            // 显示成功消息
            showMessage('保存成功', 'success');
        } else {
            showMessage('保存失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('保存失败:', error);
        showMessage('保存失败: 网络错误', 'error');
    }
}

// 处理Excel上传
async function handleExcelUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);

    try {
        showMessage('正在上传文件...', 'info');

        const response = await fetch('/api/upload_tracking_excel', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.status === 'success') {
            showMessage(result.message, 'success');
            // 刷新数据
            setTimeout(() => {
                fetchTrackingDataAndRender();
                fetchDashboardSummary();
            }, 2000);
        } else {
            showMessage('上传失败: ' + result.error, 'error');
        }
    } catch (error) {
        console.error('上传失败:', error);
        showMessage('上传失败: 网络错误', 'error');
    }

    // 清空文件输入
    event.target.value = '';
}

// 同步数据库中没有跟踪记录的物流单号到17track
async function syncTrackingFromDB() {
    try {
        showMessage('正在同步没有跟踪记录的新物流单号...', 'info');

        const response = await fetch('/api/sync_tracking_from_db', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            showMessage(result.message, 'success');
            // 延迟刷新数据，给API处理时间
            setTimeout(() => {
                fetchTrackingDataAndRender();
                fetchDashboardSummary();
            }, 3000);
        } else {
            showMessage('同步失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('同步失败:', error);
        showMessage('同步失败: 网络错误', 'error');
    }
}

// 重试失败的物流单号
async function retryFailedTracking() {
    try {
        showMessage('正在重试失败的物流单号...', 'info');

        const response = await fetch('/api/retry_failed_tracking', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.status === 'success') {
            showMessage(`开始重试 ${result.count} 个失败的物流单号，请稍后刷新查看结果`, 'success');
            // 延迟刷新数据，给API处理时间
            setTimeout(() => {
                fetchTrackingDataAndRender();
                fetchDashboardSummary();
            }, 5000);
        } else if (result.status === 'info') {
            showMessage(result.message, 'info');
        } else {
            showMessage('重试失败: ' + result.message, 'error');
        }
    } catch (error) {
        console.error('重试失败:', error);
        showMessage('重试失败: 网络错误', 'error');
    }
}

// 显示消息
function showMessage(message, type = 'info') {
    // 创建消息元素
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full`;

    // 根据类型设置样式
    const styles = {
        success: 'bg-green-500 text-white',
        error: 'bg-red-500 text-white',
        info: 'bg-blue-500 text-white',
        warning: 'bg-yellow-500 text-white'
    };

    messageDiv.className += ` ${styles[type] || styles.info}`;
    messageDiv.textContent = message;

    document.body.appendChild(messageDiv);

    // 显示动画
    setTimeout(() => {
        messageDiv.classList.remove('translate-x-full');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        messageDiv.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 300);
    }, 3000);
}

// 点击模态框外部关闭
document.getElementById('edit-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEditModal();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeEditModal();
    }
});

// 切换订单邀评状态
async function toggleOrderReview(orderNumber, isChecked) {
    try {
        // 找到该订单下的所有跟踪单号
        const orderTrackingNumbers = [];
        Object.entries(allTrackingData).forEach(([trackingNumber, item]) => {
            if (item.order_number === orderNumber) {
                orderTrackingNumbers.push(trackingNumber);
            }
        });

        // 批量更新所有跟踪单号的邀评状态
        const updatePromises = orderTrackingNumbers.map(async (trackingNumber) => {
            const item = allTrackingData[trackingNumber];
            return fetch('/api/update_local_data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    orderNumber: orderNumber,
                    originalTrackingNumber: trackingNumber,
                    newTrackingNumber: trackingNumber,
                    remark: item.remark || '',
                    isUninvitedReview: isChecked
                })
            });
        });

        // 等待所有更新完成
        await Promise.all(updatePromises);

        // 更新本地数据
        orderTrackingNumbers.forEach(trackingNumber => {
            if (allTrackingData[trackingNumber]) {
                allTrackingData[trackingNumber].is_uninvited_review = isChecked;
            }
        });

        // 重新渲染列表
        applyFilters();

        // 显示成功消息
        showMessage(isChecked ? '已标记为已邀评' : '已取消邀评标记', 'success');

    } catch (error) {
        console.error('更新邀评状态失败:', error);
        showMessage('更新邀评状态失败', 'error');

        // 恢复勾选框状态
        const checkbox = document.getElementById(`review-${orderNumber}`);
        if (checkbox) {
            checkbox.checked = !isChecked;
        }
    }
}
</script>
{% endblock %}
