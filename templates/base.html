<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}KKUGUAN{% endblock %}</title>
    <!-- 优化缓存设置 -->
    <meta http-equiv="Cache-Control" content="public, max-age=300">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <!-- 性能优化：预连接和DNS预解析 -->
    <link rel="preconnect" href="https://cdn.tailwindcss.com">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="dns-prefetch" href="https://cdn.tailwindcss.com">
    <link rel="dns-prefetch" href="https://cdn.jsdelivr.net">

    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <!-- 统一设计系统 - 优先级最高 -->
    <link href="{{ url_for('static', filename='css/unified-design-system.css') }}" rel="stylesheet">

    <!-- 蓝色主题样式 -->
    <link href="{{ url_for('static', filename='css/blue-theme.css') }}" rel="stylesheet">

    <!-- 异步加载外部资源 -->
    <script src="https://cdn.tailwindcss.com" defer></script>
    <link href="{{ url_for('static', filename='tailwind.css') }}" rel="stylesheet">

    <!-- UI组件库 -->
    <script src="{{ url_for('static', filename='js/components.js') }}" defer></script>

    <!-- 字段映射工具 - 延迟加载 -->
    <script src="{{ url_for('static', filename='js/field-mapping.js') }}" defer></script>

    <!-- 样式迁移工具 -->
    <script src="{{ url_for('static', filename='js/style-migration.js') }}" defer></script>
    <style>
        /* 全局样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html {
            min-width: 1280px;
        }
        
        body {
            margin: 0;
            padding: 0;
            min-width: 1280px;
            background: linear-gradient(to bottom right, #f3f4f6, #ffffff, #eff6ff);
        }
        
        #app-wrapper {
            width: 1280px;
            margin: 0 auto;
            padding: 0;
            position: relative;
            transform-origin: top center;
        }
        
        .container {
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 2rem;
        }
        
        .ios-blur {
            backdrop-filter: blur(12px);
            background: rgba(255,255,255,0.7);
        }
        
        /* 图表容器基础样式 */
        .chart-box {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
        }
        
        /* 确保所有内容都在容器内 */
        .content-wrapper {
            width: 100%;
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 2rem;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div id="app-wrapper">
        <!-- 统一设计导航栏 -->
        <nav class="kkuguan-nav sticky top-0 z-50">
            <div class="container">
                <div class="flex justify-between h-20 items-center">
                    <div class="flex items-center gap-4">
                        <span class="text-2xl font-bold text-blue-600 tracking-wide">KKUGUAN</span>
                    </div>
                    <div class="flex items-center gap-4">
                        <a href="{{ url_for('index') }}" class="kkuguan-nav-link">
                            <span>📊</span> 仪表板
                        </a>
                        <a href="{{ url_for('sku_detail') }}" class="kkuguan-nav-link">
                            <span>🔍</span> SKU详情
                        </a>
                        <a href="{{ url_for('recommend') }}" class="kkuguan-nav-link">
                            <span>💡</span> 选品推荐
                        </a>
                        <a href="{{ url_for('restock') }}" class="kkuguan-nav-link">
                            <span>📦</span> 补货周期
                        </a>
                        <a href="{{ url_for('alert') }}" class="kkuguan-nav-link">
                            <span>⚠️</span> 库存提醒
                        </a>
                        <a href="{{ url_for('tracking') }}" class="kkuguan-nav-link">
                            <span>🚚</span> 物流追踪
                        </a>



                        <div class="flex items-center gap-2 ml-4 pl-4 border-l border-gray-300">
                            <span class="text-sm kkuguan-text-secondary">欢迎，</span>
                            <span class="text-sm font-semibold kkuguan-text-primary">{{ session.username or '用户' }}</span>
                            <a href="{{ url_for('auth.logout') }}" class="kkuguan-btn kkuguan-btn-secondary ml-2">
                                <span>🚪</span> 退出
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- 主要内容 -->
        <main class="container py-8">
            {% block content %}{% endblock %}
        </main>
    </div>
    
    <script>
        // 页面加载完成后执行
        window.addEventListener('load', function() {
            adjustScale();
            highlightActiveNavigation();
        });

        // 窗口大小改变时执行
        window.addEventListener('resize', function() {
            adjustScale();
        });

        // 调整页面缩放
        function adjustScale() {
            const wrapper = document.getElementById('app-wrapper');
            const windowWidth = window.innerWidth;
            const scale = Math.min(windowWidth / 1280, 1);

            // 如果窗口宽度小于1280px，应用缩放
            if (windowWidth < 1280) {
                wrapper.style.transform = `scale(${scale})`;
                wrapper.style.transformOrigin = 'top center';
                // 调整body高度以适应缩放
                document.body.style.height = `${wrapper.scrollHeight * scale}px`;
            } else {
                // 如果窗口宽度大于等于1280px，重置缩放
                wrapper.style.transform = '';
                document.body.style.height = '';
            }

            // 通知图表调整大小
            if (window.dispatchEvent) {

            }
        }

        // 高亮当前页面的导航链接
        function highlightActiveNavigation() {
            const currentPath = window.location.pathname;
            const navLinks = document.querySelectorAll('.kkuguan-nav-link');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href === currentPath ||
                    (currentPath === '/' && href === '/') ||
                    (currentPath.startsWith('/sku') && href.includes('sku_detail')) ||
                    (currentPath.startsWith('/recommend') && href.includes('recommend')) ||
                    (currentPath.startsWith('/restock') && href.includes('restock')) ||
                    (currentPath.startsWith('/alert') && href.includes('alert')) ||
                    (currentPath.startsWith('/tracking') && href.includes('tracking'))) {
                    link.classList.add('active');
                }
            });
        }
    </script>
</body>
</html> 