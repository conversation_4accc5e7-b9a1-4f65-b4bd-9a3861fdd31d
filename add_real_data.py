#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime
import random
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_config import execute_query

def add_real_data():
    print('=== 添加真实业务数据 ===')
    
    # 清理现有测试数据
    print('清理现有测试数据...')
    execute_query('DELETE FROM orders WHERE order_number LIKE "TEST%" OR order_number LIKE "ORD%"')
    execute_query('DELETE FROM inventory WHERE sku LIKE "SKU%"')
    execute_query('DELETE FROM products WHERE sku LIKE "SKU%"')
    
    # 真实产品数据
    real_products = [
        # 电子产品类
        ('IPHONE15-128-BLK', 'iPhone 15 128GB 黑色', 'W3121', 5999.00),
        ('IPHONE15-256-BLU', 'iPhone 15 256GB 蓝色', 'W3121', 6999.00),
        ('IPAD-AIR-64-SLV', 'iPad Air 64GB 银色', 'W3121', 4599.00),
        ('MACBOOK-PRO-14', 'MacBook Pro 14英寸', 'W3121', 15999.00),
        ('AIRPODS-PRO-2', 'AirPods Pro 第二代', 'W3121', 1899.00),
        ('APPLE-WATCH-S9', 'Apple Watch Series 9', 'W3121', 2999.00),
        
        # 家居用品类
        ('DYSON-V15-DETECT', 'Dyson V15 Detect 吸尘器', 'H2234', 4990.00),
        ('XIAOMI-PURIFIER', '小米空气净化器 Pro H', 'H2234', 1699.00),
        ('PHILIPS-HUE-KIT', '飞利浦智能灯泡套装', 'H2234', 899.00),
        ('IROBOT-ROOMBA', 'iRobot Roomba 扫地机器人', 'H2234', 3999.00),
        
        # 服装配饰类
        ('NIKE-AIR-MAX-90', 'Nike Air Max 90 运动鞋', 'F5567', 899.00),
        ('ADIDAS-ULTRA-BOOST', 'Adidas UltraBoost 22', 'F5567', 1299.00),
        ('UNIQLO-DOWN-JACKET', '优衣库羽绒服', 'F5567', 599.00),
        ('LEVI-501-JEANS', "Levi's 501 牛仔裤", 'F5567', 699.00),
        
        # 美妆护肤类
        ('LANCOME-SERUM', '兰蔻小黑瓶精华', 'B8899', 1080.00),
        ('ESTEE-CREAM', '雅诗兰黛面霜', 'B8899', 680.00),
        ('SK2-ESSENCE', 'SK-II 神仙水', 'B8899', 1690.00),
        ('LAMER-MOISTURIZER', '海蓝之谜面霜', 'B8899', 2100.00),
        
        # 食品保健类
        ('SWISSE-VIT-C', 'Swisse 维生素C', 'N1122', 168.00),
        ('BLACKMORES-FISH-OIL', 'Blackmores 鱼油', 'N1122', 298.00),
    ]
    
    print('添加真实产品数据...')
    for sku, name, supplier, price in real_products:
        execute_query('''
            INSERT INTO products (sku, product_name, supplier_code, Price) 
            VALUES (%s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
            product_name = VALUES(product_name),
            supplier_code = VALUES(supplier_code),
            Price = VALUES(Price)
        ''', (sku, name, supplier, price))
        print(f'  ✅ {sku} - {name}')
    
    # 真实库存数据（包含一些低库存的SKU）
    today = datetime.date.today()
    real_inventory = [
        # 低库存 - 会触发提醒
        ('IPHONE15-128-BLK', 8, 3, 2, 1),      # 总库存14，高销量产品
        ('AIRPODS-PRO-2', 12, 4, 3, 2),        # 总库存21，热销产品
        ('NIKE-AIR-MAX-90', 6, 2, 1, 1),       # 总库存10，季节性产品
        ('LANCOME-SERUM', 15, 5, 3, 2),        # 总库存25，美妆热销
        ('SWISSE-VIT-C', 20, 8, 5, 3),         # 总库存36，保健品
        
        # 中等库存
        ('IPHONE15-256-BLU', 45, 15, 10, 8),   # 总库存78
        ('IPAD-AIR-64-SLV', 35, 12, 8, 5),     # 总库存60
        ('DYSON-V15-DETECT', 25, 8, 5, 3),     # 总库存41
        ('XIAOMI-PURIFIER', 60, 20, 15, 10),   # 总库存105
        ('ADIDAS-ULTRA-BOOST', 40, 15, 10, 8), # 总库存73
        
        # 高库存
        ('MACBOOK-PRO-14', 80, 25, 15, 12),    # 总库存132，高价值产品
        ('APPLE-WATCH-S9', 120, 40, 25, 20),   # 总库存205
        ('PHILIPS-HUE-KIT', 150, 50, 30, 25),  # 总库存255
        ('IROBOT-ROOMBA', 90, 30, 20, 15),     # 总库存155
        ('UNIQLO-DOWN-JACKET', 200, 80, 50, 40), # 总库存370，季节性大量备货
        ('LEVI-501-JEANS', 180, 60, 40, 30),   # 总库存310
        ('ESTEE-CREAM', 100, 35, 20, 15),      # 总库存170
        ('SK2-ESSENCE', 75, 25, 15, 12),       # 总库存127
        ('LAMER-MOISTURIZER', 50, 18, 12, 8),  # 总库存88，高端产品
        ('BLACKMORES-FISH-OIL', 300, 100, 60, 50), # 总库存510，保健品大量备货
    ]
    
    print('\n添加真实库存数据...')
    for sku, qty, account, sl, gwyj in real_inventory:
        total_stock = qty + account + sl + gwyj
        execute_query('''
            INSERT INTO inventory (sku, quantity, account_stock, sl_quantity, gwyj_quantity, 
                                 estimated_restock_date, estimated_restock_qty, date) 
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
            quantity = VALUES(quantity),
            account_stock = VALUES(account_stock),
            sl_quantity = VALUES(sl_quantity),
            gwyj_quantity = VALUES(gwyj_quantity),
            estimated_restock_date = VALUES(estimated_restock_date),
            estimated_restock_qty = VALUES(estimated_restock_qty),
            date = VALUES(date)
        ''', (sku, qty, account, sl, gwyj, '2025-08-15', str(qty * 2), today))
        print(f'  ✅ {sku} - 库存: {total_stock}')
    
    # 真实订单数据（最近30天）
    print('\n添加真实订单数据（最近30天）...')
    
    # 高销量产品的订单
    high_sales_products = [
        ('IPHONE15-128-BLK', 25),    # 30天内销售25台
        ('AIRPODS-PRO-2', 18),       # 30天内销售18个
        ('NIKE-AIR-MAX-90', 12),     # 30天内销售12双
        ('LANCOME-SERUM', 15),       # 30天内销售15瓶
        ('SWISSE-VIT-C', 22),        # 30天内销售22瓶
    ]
    
    # 中等销量产品的订单
    medium_sales_products = [
        ('IPHONE15-256-BLU', 8),
        ('IPAD-AIR-64-SLV', 6),
        ('DYSON-V15-DETECT', 4),
        ('XIAOMI-PURIFIER', 7),
        ('ADIDAS-ULTRA-BOOST', 5),
    ]
    
    # 低销量产品的订单
    low_sales_products = [
        ('MACBOOK-PRO-14', 2),
        ('APPLE-WATCH-S9', 3),
        ('PHILIPS-HUE-KIT', 4),
        ('IROBOT-ROOMBA', 2),
        ('UNIQLO-DOWN-JACKET', 6),
        ('LEVI-501-JEANS', 4),
        ('ESTEE-CREAM', 3),
        ('SK2-ESSENCE', 2),
        ('LAMER-MOISTURIZER', 1),
        ('BLACKMORES-FISH-OIL', 8),
    ]
    
    order_counter = 1
    
    # 生成订单数据
    for products_list in [high_sales_products, medium_sales_products, low_sales_products]:
        for sku, total_sales in products_list:
            # 将销量分散到30天内的随机日期
            for _ in range(total_sales):
                days_ago = random.randint(1, 30)
                order_date = today - datetime.timedelta(days=days_ago)
                quantity = random.choice([1, 1, 1, 2, 2, 3])  # 大部分订单是1-2个
                
                order_number = f'KK{order_date.strftime("%Y%m%d")}{order_counter:04d}'
                tracking_number = f'TN{order_date.strftime("%m%d")}{order_counter:04d}'
                
                # 随机状态
                status = random.choice(['Delivered', 'Delivered', 'Delivered', 'In Transit', 'Shipped'])
                
                execute_query('''
                    INSERT INTO orders (order_number, sku, product_quantity, order_date, 
                                      tracking_number, status, platform_channel, store_account) 
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ''', (order_number, sku, quantity, order_date, tracking_number, status, 
                      random.choice(['Amazon', 'eBay', '天猫', '京东']), 
                      random.choice(['Store_A', 'Store_B', 'Store_C'])))
                
                order_counter += 1
    
    print(f'  ✅ 生成了 {order_counter-1} 个订单')
    
    # 验证数据
    print('\n=== 数据验证 ===')
    products_count = execute_query('SELECT COUNT(*) as count FROM products')
    print(f'Products表记录数: {products_count[0]["count"]}')
    
    inventory_count = execute_query('SELECT COUNT(*) as count FROM inventory')
    print(f'Inventory表记录数: {inventory_count[0]["count"]}')
    
    orders_count = execute_query('SELECT COUNT(*) as count FROM orders')
    print(f'Orders表记录数: {orders_count[0]["count"]}')
    
    # 检查会触发提醒的SKU
    print('\n=== 检查低库存提醒 ===')
    alerts_query = '''
    SELECT
        i.sku,
        i.quantity as current_stock,
        COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0) as total_stock,
        COALESCE(s.total_sales_30d, 0) as sales_30d,
        CASE
            WHEN COALESCE(s.total_sales_30d, 0) > 0 THEN
                ROUND((COALESCE(i.quantity, 0) + COALESCE(i.sl_quantity, 0) + COALESCE(i.gwyj_quantity, 0)) * 30 / s.total_sales_30d, 1)
            ELSE 999999
        END as estimated_days
    FROM inventory i
    LEFT JOIN (
        SELECT sku, SUM(product_quantity) as total_sales_30d
        FROM orders
        WHERE order_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY sku
    ) s ON i.sku = s.sku
    WHERE i.quantity > 0
    AND i.date = (SELECT MAX(i2.date) FROM inventory i2 WHERE i2.sku = i.sku)
    HAVING estimated_days <= 60
    ORDER BY estimated_days ASC
    '''
    
    alerts = execute_query(alerts_query)
    print(f'预计会有 {len(alerts)} 个低库存提醒:')
    for alert in alerts:
        print(f'  📢 {alert["sku"]}: 总库存{alert["total_stock"]}, 30天销量{alert["sales_30d"]}, 预计{alert["estimated_days"]}天')
    
    print('\n✅ 真实数据添加完成！现在可以测试提醒功能了。')

if __name__ == '__main__':
    add_real_data()
