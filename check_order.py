#!/usr/bin/env python3
"""
检查特定订单的物流追踪状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database_config import execute_query
import json

def check_order_tracking(order_number):
    """检查订单的物流追踪状态"""
    
    # 查询order_tracking_details表
    tracking_query = """
    SELECT tracking_number, carrier_code, status, latest_event_description, 
           latest_event_location, latest_event_time, tracking_info, 
           created_at, updated_at
    FROM order_tracking_details 
    WHERE tracking_number LIKE %s OR tracking_number = %s
    """
    
    tracking_results = execute_query(tracking_query, (f'%{order_number}%', order_number))
    
    print(f"=== 订单 {order_number} 的物流追踪详情 ===")
    
    if tracking_results:
        for result in tracking_results:
            print(f"\n物流单号: {result['tracking_number']}")
            print(f"运输商代码: {result['carrier_code']}")
            print(f"状态: {result['status']}")
            print(f"最新事件描述: {result['latest_event_description']}")
            print(f"最新事件位置: {result['latest_event_location']}")
            print(f"最新事件时间: {result['latest_event_time']}")
            print(f"创建时间: {result['created_at']}")
            print(f"更新时间: {result['updated_at']}")
            
            # 解析tracking_info
            if result['tracking_info']:
                try:
                    tracking_info = json.loads(result['tracking_info']) if isinstance(result['tracking_info'], str) else result['tracking_info']
                    print(f"完整追踪信息: {json.dumps(tracking_info, indent=2, ensure_ascii=False)}")
                except:
                    print(f"追踪信息解析失败: {result['tracking_info']}")
    else:
        print("未找到相关的物流追踪记录")
    
    # 查询orders表
    orders_query = """
    SELECT order_number, tracking_number, platform_channel, store_account, 
           remark, is_uninvited_review, updated_at
    FROM orders 
    WHERE order_number LIKE %s OR order_number = %s OR tracking_number LIKE %s
    """
    
    orders_results = execute_query(orders_query, (f'%{order_number}%', order_number, f'%{order_number}%'))
    
    print(f"\n=== 订单表中的相关记录 ===")
    
    if orders_results:
        for result in orders_results:
            print(f"\n订单号: {result['order_number']}")
            print(f"物流单号: {result['tracking_number']}")
            print(f"平台: {result['platform_channel']}")
            print(f"店铺: {result['store_account']}")
            print(f"备注: {result['remark']}")
            print(f"邀评状态: {result['is_uninvited_review']}")
            print(f"更新时间: {result['updated_at']}")
    else:
        print("未找到相关的订单记录")

if __name__ == "__main__":
    order_number = "113-3848577-7425813"
    check_order_tracking(order_number)

    # 同时检查实际的物流单号
    print("\n" + "="*50)
    tracking_number = "************"
    check_order_tracking(tracking_number)
