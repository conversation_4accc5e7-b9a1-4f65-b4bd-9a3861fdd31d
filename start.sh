#!/bin/bash
# KKUGUAN 启动脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_DIR"

# 日志目录
mkdir -p logs

echo -e "${BLUE}🚀 KKUGUAN 库存管理系统启动脚本${NC}"
echo "=================================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装${NC}"
    exit 1
fi

# 检查依赖
echo -e "${YELLOW}📦 检查依赖...${NC}"
if ! python3 -c "import flask" &> /dev/null; then
    echo -e "${RED}❌ Flask 未安装，请运行: pip install -r requirements.txt${NC}"
    exit 1
fi

# 检查Redis
echo -e "${YELLOW}🔍 检查Redis服务...${NC}"
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo -e "${GREEN}✅ Redis 服务正常${NC}"
    else
        echo -e "${YELLOW}⚠️ Redis 服务未启动，尝试启动...${NC}"
        if command -v brew &> /dev/null; then
            brew services start redis
        else
            echo -e "${YELLOW}⚠️ 请手动启动Redis服务${NC}"
        fi
    fi
else
    echo -e "${YELLOW}⚠️ Redis 未安装，将使用内存缓存${NC}"
fi

# 选择启动模式
echo ""
echo "请选择启动模式:"
echo "1) 开发模式 (python3 app.py) - 支持调试和热重载"
echo "2) 生产模式 (gunicorn) - 高性能多进程"
echo "3) 后台模式 (nohup) - 后台运行"
echo ""
read -p "请输入选择 (1-3): " choice

case $choice in
    1)
        echo -e "${GREEN}🔧 启动开发模式...${NC}"
        echo "按 Ctrl+C 停止服务"
        python3 app.py
        ;;
    2)
        echo -e "${GREEN}🚀 启动生产模式...${NC}"
        if ! python3 -c "import gunicorn" &> /dev/null; then
            echo -e "${YELLOW}📦 安装Gunicorn...${NC}"
            pip install gunicorn gevent
        fi
        
        echo "启动Gunicorn服务器..."
        gunicorn \
            --bind 0.0.0.0:5003 \
            --workers 4 \
            --worker-class gevent \
            --worker-connections 1000 \
            --max-requests 1000 \
            --max-requests-jitter 100 \
            --timeout 120 \
            --keepalive 5 \
            --preload-app \
            --access-logfile logs/access.log \
            --error-logfile logs/error.log \
            --log-level info \
            wsgi:app
        ;;
    3)
        echo -e "${GREEN}🌙 启动后台模式...${NC}"
        nohup python3 app.py > logs/app.log 2>&1 &
        PID=$!
        echo $PID > kkuguan.pid
        echo -e "${GREEN}✅ 服务已在后台启动 (PID: $PID)${NC}"
        echo "🌐 访问地址: http://localhost:5003"
        echo "📝 日志文件: logs/app.log"
        echo "🛑 停止服务: kill $PID"
        ;;
    *)
        echo -e "${RED}❌ 无效选择${NC}"
        exit 1
        ;;
esac
